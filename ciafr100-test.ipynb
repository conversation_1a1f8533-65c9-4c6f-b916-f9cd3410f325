import torch
import torch.nn as nn
import torch.optim as optim
from torchvision import datasets, transforms, models
from torch.utils.data import DataLoader
import torch
import torch.nn as nn
import copy
import numpy as np


# 使用 GPU，如果可用
device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
from xgboost import XGBClassifier
from torch.nn import functional as F
from sklearn.metrics import classification_report, accuracy_score, f1_score
from sklearn.model_selection import train_test_split
import pickle
import pandas as pd
import matplotlib.pyplot as plt
from pandas.plotting import parallel_coordinates
from mpl_toolkits.mplot3d import Axes3D
from sklearn.metrics import roc_auc_score
# def MIA_data(shadow_model, in_data, out_data):
#     # in_data = in_data.float()  # 将输入数据转换为 FloatTensor
#     in_data = in_data.to(device)

#     # out_data = out_data.float()  # 将输入数据转换为 FloatTensor
#     out_data = out_data.to(device)
#     # important！！！
#     shadow_model.eval()
    
#     attack_x = []
#     attack_y = []

#     pred = F.softmax(shadow_model(in_data), dim=1)
#     pred, _ = torch.sort(pred, descending=True)
#     pred = pred[:,:3]
#     print(pred[0])
#     attack_x.append(pred.detach())
#     attack_y.append(torch.ones(size=(in_data.shape[0],)).to(device))

#     pred = F.softmax(shadow_model(out_data), dim=1)
#     pred, _ = torch.sort(pred, descending=True)
#     pred = pred[:,:3]
#     print(pred[0])

#     attack_x.append(pred.detach())
#     attack_y.append(torch.zeros(size=(out_data.shape[0],)).to(device))

#     tensor_x = torch.cat(attack_x)
#     tensor_y = torch.cat(attack_y)

#     # return attackloader, attacktester
#     data = tensor_x.detach().cpu().numpy()
#     target = tensor_y.detach().cpu().numpy()

#     return data, target


def MIA_data(shadow_model, in_data, out_data):
    # 将数据传输到GPU


    in_data = in_data.to(device)
    out_data = out_data.to(device)

    # 将shadow_model放在DataParallel中，让其自动使用所有可用GPU
    # shadow_model = nn.DataParallel(shadow_model)

    # important！！！
    shadow_model.eval()

    attack_x = []
    attack_y = []

    pred = F.softmax(shadow_model(in_data), dim=1)
    pred, _ = torch.sort(pred, descending=True)
    pred = pred[:,:3]
    pred_in = pred[0]
    for i in range(len(pred)):
        if(i>1):
            pred_in = pred_in + pred[i]
    print(pred_in/len(pred))
    attack_x.append(pred.detach())
    attack_y.append(torch.ones(size=(in_data.shape[0],)).to(device))

    pred = F.softmax(shadow_model(out_data), dim=1)
    pred, _ = torch.sort(pred, descending=True)
    pred = pred[:,:3]
    pred_out = pred[0]
    for i in range(len(pred)):
        if(i>1):
            pred_out = pred_out + pred[i]
    print(pred_out/len(pred))

    attack_x.append(pred.detach())
    attack_y.append(torch.zeros(size=(out_data.shape[0],)).to(device))

    tensor_x = torch.cat(attack_x)
    tensor_y = torch.cat(attack_y)

    # 将数据移回到CPU
    data = tensor_x.detach().cpu().numpy()
    target = tensor_y.detach().cpu().numpy()

    return data, target




# def train_attacker(data_x, data_y):

#     X_train, X_test, y_train, y_test = train_test_split(data_x, data_y, test_size=0.5)

#     attack_model = XGBClassifier(n_jobs=4, objective='binary:logistic', booster="gbtree")
#     attack_model.fit(X_train, y_train)
#     pickle.dump(attack_model, open('./save_model/MIA_attackModel.pkl', 'wb'))

#     print("MIA Attacker training accuracy: {}".format(accuracy_score(y_train, attack_model.predict(X_train))))
#     print("MIA Attacker testing accuracy: {}".format(accuracy_score(y_test, attack_model.predict(X_test))))
#     return accuracy_score(y_train, attack_model.predict(X_train)),accuracy_score(y_test, attack_model.predict(X_test))
def train_attacker(data_x, data_y):
    X_train, X_test, y_train, y_test = train_test_split(data_x, data_y, test_size=0.2)

    attack_model = XGBClassifier(n_jobs=4, objective='binary:logistic', booster="gbtree")
    attack_model.fit(X_train, y_train)
    pickle.dump(attack_model, open('./save_model/MIA_attackModel.pkl', 'wb'))

    y_train_pred = attack_model.predict(X_train)
    y_test_pred = attack_model.predict(X_test)

    # 计算预测概率，以便计算AUC
    y_train_pred_prob = attack_model.predict_proba(X_train)[:, 1]
    y_test_pred_prob = attack_model.predict_proba(X_test)[:, 1]

    print("MIA Attacker training accuracy: {}".format(accuracy_score(y_train, y_train_pred)))
    print("MIA Attacker testing accuracy: {}".format(accuracy_score(y_test, y_test_pred)))

    print("Training AUC: {}".format(roc_auc_score(y_train, y_train_pred_prob)))
    print("Testing AUC: {}".format(roc_auc_score(y_test, y_test_pred_prob)))

    print("Training classification report:")
    print(classification_report(y_train, y_train_pred))

    print("Testing classification report:")
    print(classification_report(y_test, y_test_pred))

    # Plot the scatter plot
    member_data = X_test[y_test == 1]
    non_member_data = X_test[y_test == 0]


    # 创建 3D 散点图
    fig = plt.figure()
    ax = fig.add_subplot(111, projection='3d')

    # 用前三个概率绘制散点图
    ax.scatter(member_data[:, 0], member_data[:, 1], member_data[:, 2], c='red', label='Member')
    ax.scatter(non_member_data[:, 0], non_member_data[:, 1], non_member_data[:, 2], c='blue', label='Non-Member')

    ax.set_xlabel('First Probability')
    ax.set_ylabel('Second Probability')
    ax.set_zlabel('Third Probability')
    ax.legend()
    ax.set_title('Member vs Non-Member Data Scatter Plot (3D)')

    plt.show()

    return accuracy_score(y_train, attack_model.predict(X_train)), accuracy_score(y_test, attack_model.predict(X_test))



import numpy as np
import torch
from torch.nn import functional as F
from sklearn.metrics import classification_report, accuracy_score, f1_score
from sklearn.model_selection import train_test_split
from xgboost import XGBClassifier
import pickle
import warnings
from functools import partial
from torchvision.models import resnet
import torch
import torch.nn as nn
import copy
import numpy as np
import torch
from torch.utils.data import DataLoader
from torchvision import transforms
from torchvision.datasets import CIFAR100
from PIL import Image
from sklearn.metrics.pairwise import cosine_similarity
import argparse
import copy
import numpy as np
import torch
from torch.utils.data import DataLoader
from torchvision import transforms
from torchvision.datasets import SVHN,CIFAR100
from PIL import Image
import math
import logging
#moco
"""
Define MoCo wrapper
"""

class SplitBatchNorm(nn.BatchNorm2d):
    def __init__(self, num_features, num_splits, **kw):
        super().__init__(num_features, **kw)
        self.num_splits = num_splits
        
    def forward(self, input):
        N, C, H, W = input.shape
        if self.training or not self.track_running_stats:
            running_mean_split = self.running_mean.repeat(self.num_splits)
            running_var_split = self.running_var.repeat(self.num_splits)
            outcome = nn.functional.batch_norm(
                input.view(-1, C * self.num_splits, H, W), running_mean_split, running_var_split, 
                self.weight.repeat(self.num_splits), self.bias.repeat(self.num_splits),
                True, self.momentum, self.eps).view(N, C, H, W)
            self.running_mean.data.copy_(running_mean_split.view(self.num_splits, C).mean(dim=0))
            self.running_var.data.copy_(running_var_split.view(self.num_splits, C).mean(dim=0))
            return outcome
        else:
            return nn.functional.batch_norm(
                input, self.running_mean, self.running_var, 
                self.weight, self.bias, False, self.momentum, self.eps)

class ModelBase(nn.Module):
    """
    Common CIFAR ResNet recipe.
    Comparing with ImageNet ResNet recipe, it:
    (i) replaces conv1 with kernel=3, str=1
    (ii) removes pool1
    """
    def __init__(self, feature_dim=128, arch=None, bn_splits=16):
        super(ModelBase, self).__init__()

        # use split batchnorm
        super(ModelBase, self).__init__()

        # use split batchnorm
        norm_layer = partial(SplitBatchNorm, num_splits=bn_splits) if bn_splits > 1 else nn.BatchNorm2d
        resnet_arch = getattr(resnet, arch)
        net = resnet_arch(num_classes=feature_dim, norm_layer=norm_layer)

        self.named_layers = {}
        self.net = []
        for name, module in net.named_children():
            if name == 'conv1':
                module = nn.Conv2d(3, 64, kernel_size=3, stride=1, padding=1, bias=False)
            if isinstance(module, nn.MaxPool2d):
                continue
            if isinstance(module, nn.Linear):
                self.net.append(nn.Flatten(1))
            self.named_layers[name] = module
            self.net.append(module)

        self.net = nn.Sequential(*self.net)

    def forward(self, x, layer_name=None):
        if layer_name is None:
            x = self.net(x)
            # note: not normalized here
            return x
        else:
            for name, module in self.named_layers.items():
                x = module(x)
                if name == layer_name:
                    if len(x.shape) > 2:
                        x = x.view(x.size(0), -1)  # Flatten the tensor
                    return x





class ModelMoCo(nn.Module):
    def __init__(self, dim=128, K=4096, m=0.99, T=0.1, arch='resnet18', bn_splits=8, symmetric=True):
        super(ModelMoCo, self).__init__()

        self.K = K
        self.m = m
        self.T = T
        self.symmetric = symmetric

        # create the encoders
        self.encoder_q = ModelBase(feature_dim=dim, arch=arch, bn_splits=bn_splits)
        self.encoder_k = ModelBase(feature_dim=dim, arch=arch, bn_splits=bn_splits)

        for param_q, param_k in zip(self.encoder_q.parameters(), self.encoder_k.parameters()):
            param_k.data.copy_(param_q.data)  # initialize
            param_k.requires_grad = False  # not update by gradient

        # create the queue
        self.register_buffer("queue", torch.randn(dim, K))
        self.queue = nn.functional.normalize(self.queue, dim=0)

        self.register_buffer("queue_ptr", torch.zeros(1, dtype=torch.long))

    @torch.no_grad()
    def _momentum_update_key_encoder(self):
        """
        Momentum update of the key encoder
        """
        for param_q, param_k in zip(self.encoder_q.parameters(), self.encoder_k.parameters()):
            param_k.data = param_k.data * self.m + param_q.data * (1. - self.m)

    @torch.no_grad()
    def _dequeue_and_enqueue(self, keys):
        batch_size = keys.shape[0]

        ptr = int(self.queue_ptr)
        # print('self.K:',self.K)
        # print('batch_size:',batch_size)
        assert self.K % batch_size == 0  # for simplicity

        # replace the keys at ptr (dequeue and enqueue)
        self.queue[:, ptr:ptr + batch_size] = keys.t()  # transpose
        ptr = (ptr + batch_size) % self.K  # move pointer

        self.queue_ptr[0] = ptr

    @torch.no_grad()
    def _batch_shuffle_single_gpu(self, x):
        """
        Batch shuffle, for making use of BatchNorm.
        """
        # random shuffle index
        idx_shuffle = torch.randperm(x.shape[0]).to(device)

        # index for restoring
        idx_unshuffle = torch.argsort(idx_shuffle)

        return x[idx_shuffle], idx_unshuffle

    @torch.no_grad()
    def _batch_unshuffle_single_gpu(self, x, idx_unshuffle):
        """
        Undo batch shuffle.
        """
        return x[idx_unshuffle]

    def contrastive_loss(self, im_q, im_k):
        # compute query features
        q = self.encoder_q(im_q)  # queries: NxC
        q = nn.functional.normalize(q, dim=1)  # already normalized

        # compute key features
        with torch.no_grad():  # no gradient to keys
            # shuffle for making use of BN
            im_k_, idx_unshuffle = self._batch_shuffle_single_gpu(im_k)

            k = self.encoder_k(im_k_)  # keys: NxC
            k = nn.functional.normalize(k, dim=1)  # already normalized

            # undo shuffle
            k = self._batch_unshuffle_single_gpu(k, idx_unshuffle)

        # compute logits
        # Einstein sum is more intuitive
        # positive logits: Nx1
        l_pos = torch.einsum('nc,nc->n', [q, k]).unsqueeze(-1)
        # negative logits: NxK
        l_neg = torch.einsum('nc,ck->nk', [q, self.queue.clone().detach()])

        # logits: Nx(1+K)
        logits = torch.cat([l_pos, l_neg], dim=1)

        # apply temperature
        logits /= self.T

        # labels: positive key indicators
        labels = torch.zeros(logits.shape[0], dtype=torch.long).to(device)
        
        loss = nn.CrossEntropyLoss().to(device)(logits, labels)

        return loss, q, k

    def forward(self, im1, im2):
        """
        Input:
            im_q: a batch of query images
            im_k: a batch of key images
        Output:
            loss
        """

        # update the key encoder
        with torch.no_grad():  # no gradient to keys
            self._momentum_update_key_encoder()

        # compute loss
        if self.symmetric:  # asymmetric loss
            loss_12, q1, k2 = self.contrastive_loss(im1, im2)
            loss_21, q2, k1 = self.contrastive_loss(im2, im1)
            loss = loss_12 + loss_21
            k = torch.cat([k1, k2], dim=0)
        else:  # asymmetric loss
            loss, q, k = self.contrastive_loss(im1, im2)

        self._dequeue_and_enqueue(k)

        return loss
    
    def compute_loss_per_sample(self, im_q, im_k):
        # compute query features
        q = self.encoder_q(im_q)  # queries: NxC
        q = nn.functional.normalize(q, dim=1)  # already normalized

        # compute key features
        with torch.no_grad():  # no gradient to keys
            # shuffle for making use of BN
            im_k_, idx_unshuffle = self._batch_shuffle_single_gpu(im_k)

            k = self.encoder_k(im_k_)  # keys: NxC
            k = nn.functional.normalize(k, dim=1)  # already normalized

            # undo shuffle
            k = self._batch_unshuffle_single_gpu(k, idx_unshuffle)

        # compute logits
        # positive logits: Nx1
        l_pos = torch.einsum('nc,nc->n', [q, k]).unsqueeze(-1)
        # negative logits: NxK
        l_neg = torch.einsum('nc,ck->nk', [q, self.queue.clone().detach()])

        # logits: Nx(1+K)
        logits = torch.cat([l_pos, l_neg], dim=1)

        # apply temperature
        logits /= self.T

        # labels: positive key indicators
        labels = torch.zeros(logits.shape[0], dtype=torch.long).to(device)
        
        # return per-sample loss
        loss_fn = nn.CrossEntropyLoss(reduction='none').to(device)
        losses = loss_fn(logits, labels)

        return losses




def load_shadow_indices():
    fname = './data_indices/shadow_data_indices_cifar100.npz'
    with np.load(fname) as f:
        indices = [f['arr_%d' % i] for i in range(len(f.files))]
    return indices
def load_target_indices():
    fname = './data_indices/target_data_indices_cifar100.npz'
    with np.load(fname) as f:
        indices = [f['arr_%d' % i] for i in range(len(f.files))]
    return indices
class CIFAR100Pair(CIFAR100):
    """CIFAR100 Dataset.
    """
    def __getitem__(self, index):
        img = self.data[index]
        targets = self.targets[index]
        img = Image.fromarray(img)
        if self.transform is not None:
            im_1 = self.transform(img)
            im_2 = self.transform(img)

        return im_1, im_2

train_transform = transforms.Compose([
    transforms.RandomResizedCrop(32, scale=(0.2, 1.)),
    transforms.RandomHorizontalFlip(p=0.5),
    transforms.RandomApply([transforms.ColorJitter(0.4, 0.4, 0.4, 0.1)], p=0.8),
    transforms.RandomGrayscale(p=0.2),
    transforms.ToTensor(),
    transforms.Normalize([0.4914, 0.4822, 0.4465], [0.2023, 0.1994, 0.2010])])

test_transform = transforms.Compose([
    transforms.ToTensor(),
    transforms.Normalize([0.4914, 0.4822, 0.4465], [0.2023, 0.1994, 0.2010])])

# data prepare
train_data = CIFAR100Pair(root='data', train=True, transform=train_transform, download=True)
train_loader = DataLoader(train_data, batch_size=512, shuffle=True, num_workers=16, pin_memory=True, drop_last=True)

memory_data = CIFAR100(root='data', train=True, transform=train_transform, download=True)
memory_loader = DataLoader(memory_data, batch_size=512, shuffle=False, num_workers=16, pin_memory=True)

test_data = CIFAR100(root='data', train=False, transform=test_transform, download=True)
test_loader = DataLoader(test_data, batch_size=512, shuffle=False, num_workers=16, pin_memory=True)
data_dir = './data/'
indices_shadow = load_shadow_indices()[0]
indices_target = load_target_indices()[0]
indices_shadow_train = indices_shadow [:2000]
out_train = indices_shadow [10000:12000]
train_shadow = indices_shadow [:2000]
mia_out = indices_target[:2000]
traing_shadowin_data = indices_shadow [:10000]
traing_shadowin_data_target = indices_shadow [10000:20000]
# indices_shadow_test = indices_shadow [10000:12000]
indices_target = load_target_indices()[0]
train_test = indices_shadow [:10000]
total_train_indices = np.arange(50000) 

remaining_indices = np.setdiff1d(total_train_indices, indices_shadow)  # 从所有训练数据索引中删除 shadow_indices

num_samples = 2000  # 我们想要从剩余的训练数据索引中随机选择的样本数

random_indices = np.random.choice(remaining_indices, num_samples, replace=False)

indices_shadow_test = np.random.choice(np.arange(10000), num_samples, replace=False)

train_data_target = torch.utils.data.Subset(dataset=train_data, indices=indices_target)
train_loader = DataLoader(train_data_target, batch_size=512, shuffle=True, num_workers=16, pin_memory=True, drop_last=True)

training_data = torch.utils.data.Subset(dataset=train_data, indices=traing_shadowin_data)
training_moco_loader = DataLoader(training_data, batch_size=512, shuffle=True, num_workers=16, pin_memory=True, drop_last=True)
training_data_target = torch.utils.data.Subset(dataset=train_data, indices=traing_shadowin_data_target)
training_moco_loader_target = DataLoader(training_data_target, batch_size=512, shuffle=True, num_workers=16, pin_memory=True, drop_last=True)


ACC_shadow = torch.utils.data.Subset(dataset=memory_data, indices=train_test)
ACC_loader = DataLoader(ACC_shadow, batch_size=512, shuffle=False, num_workers=16, pin_memory=True)
ACC_shadow_targets = np.array(memory_data.targets)[train_test]

train_data_shadow = torch.utils.data.Subset(dataset=train_data, indices=train_shadow)
train_sub_loader = DataLoader(train_data_shadow, batch_size=512, shuffle=True, num_workers=16, pin_memory=True, drop_last=True)

out_shadow = torch.utils.data.Subset(dataset=train_data, indices=out_train)
out_loader = DataLoader(out_shadow, batch_size=512, shuffle=True, num_workers=16, pin_memory=True, drop_last=True)




memory_data_shadow = torch.utils.data.Subset(dataset=memory_data, indices=indices_shadow_train)
memory_sub_loader = DataLoader(memory_data_shadow, batch_size=512, shuffle=False, num_workers=16, pin_memory=True)
memory_data_shadow_targets = np.array(memory_data.targets)[indices_shadow_train]

test_data_shadow = torch.utils.data.Subset(dataset=memory_data, indices=out_train)
test_sub_loader = DataLoader(test_data_shadow, batch_size=512, shuffle=False, num_workers=16, pin_memory=True)
test_mia_data = torch.utils.data.Subset(dataset=test_data, indices=indices_shadow_test)
mia_out_loader = DataLoader(test_mia_data, batch_size=512, shuffle=False, num_workers=16, pin_memory=True)

print("waiting...")


train_data_shadow = torch.utils.data.Subset(dataset=memory_data, indices=traing_shadowin_data)
train_data_loader = DataLoader(memory_data_shadow, batch_size=512, shuffle=False, num_workers=16, pin_memory=True)
test_data_sup = torch.utils.data.Subset(dataset=test_data, indices=indices_shadow_test)
test_data_loader = DataLoader(test_data_sup, batch_size=512, shuffle=False, num_workers=16, pin_memory=True, drop_last=True)

train_datasets = []
train_targets = []
for i, (data, target) in enumerate(train_data_loader):
    train_datasets.append(data.cpu().numpy())
    train_targets.append(target.cpu().numpy())
train_datasets = np.concatenate(train_datasets)
train_targets = np.concatenate(train_targets)

test_datasets = []
test_targets = []
for i, (data, target) in enumerate(mia_out_loader):
    test_datasets.append(data.cpu().numpy())
    test_targets.append(target.cpu().numpy())
test_datasets = np.concatenate(test_datasets)
test_targets = np.concatenate(test_targets)
print("saving...")
np.savez('logs/cifar100train.npz', x=train_datasets, y=train_targets)
np.savez('logs/cifar100test.npz', x=test_datasets, y=test_targets)
print("ok")
# test data
test_data = torch.from_numpy(np.load('logs/cifar100train.npz')['x'])
test_label = torch.from_numpy(np.load('logs/cifar100train.npz')['y'])
shadowin = test_data, test_label
# test data
test_data = torch.from_numpy(np.load('logs/cifar100test.npz')['x'])
test_label = torch.from_numpy(np.load('logs/cifar100test.npz')['y'])
shadowout = test_data, test_label

import torch.nn.functional as F
from torch.utils.data import DataLoader
from urllib.request import urlopen
from datetime import datetime
from functools import partial
from PIL import Image
from torch.utils.data import DataLoader
from torchvision import transforms
from torchvision.datasets import CIFAR10
from torchvision.models import resnet
from tqdm import tqdm
import argparse
import json
import math
import os
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
def test(net, memory_data_loader, test_data_loader, memory_data_target_targets):
    net.eval()
    classes = 100
    total_top1, total_top5, total_num, feature_bank = 0.0, 0.0, 0, []
    with torch.no_grad():
        # generate feature bank
        for data, target in tqdm(memory_data_loader, desc='Feature extracting'):
            feature = net(data.to(device))
            feature = F.normalize(feature, dim=1)
            feature_bank.append(feature)
        # [D, N]
        feature_bank = torch.cat(feature_bank, dim=0).t().contiguous()
        # [N]
        feature_labels = torch.tensor(memory_data_target_targets, device=feature_bank.device)
        # loop test data to predict the label by weighted knn search
        test_bar = tqdm(test_data_loader)
        for data, target in test_bar:
            data, target = data.to(device), target.to(device)
            feature = net(data)
            feature = F.normalize(feature, dim=1)
            
            pred_labels = knn_predict(feature, feature_bank, feature_labels, classes, 200, 0.1)

            total_num += data.size(0)
            total_top1 += (pred_labels[:, 0] == target).float().sum().item()
            test_bar.set_description('Acc@1:{:.2f}%'.format(total_top1 / total_num * 100))
    return total_top1 / total_num * 100


def knn_predict(feature, feature_bank, feature_labels, classes, knn_k, knn_t):
    # compute cos similarity between each feature vector and feature bank ---> [B, N]
    # 计算每个特征向量和特征库之间的cos相似度 ---> [B, N]
    sim_matrix = torch.mm(feature, feature_bank)
    # [B, K]
    sim_weight, sim_indices = sim_matrix.topk(k=knn_k, dim=-1)
    # [B, K]
    sim_labels = torch.gather(feature_labels.expand(feature.size(0), -1), dim=-1, index=sim_indices)
    sim_weight = (sim_weight / knn_t).exp()

    # counts for each class
    one_hot_label = torch.zeros(feature.size(0) * knn_k, classes, device=sim_labels.device)
    # [B*K, C]
    one_hot_label = one_hot_label.scatter(dim=-1, index=sim_labels.view(-1, 1), value=1.0)
    # weighted score ---> [B, C]
    pred_scores = torch.sum(one_hot_label.view(feature.size(0), -1, classes) * sim_weight.unsqueeze(dim=-1), dim=1)

    pred_labels = pred_scores.argsort(dim=-1, descending=True)
    return pred_labels

import os
from unittest import TestLoader
import numpy as np
from torch.autograd import Variable
import torch.nn as nn
import torch.autograd as autograd
import torch
import csv
import matplotlib.pyplot as plt
import torchvision
from tqdm import tqdm
import time
from torch.utils.data import DataLoader
import torch.nn.functional as F
cuda = torch.device("cuda:1" if torch.cuda.is_available() else "cpu")
Tensor = torch.cuda.FloatTensor if cuda else torch.FloatTensor
#计算惩罚梯度
def compute_gradient_penalty(D, real_samples, fake_samples):
    """Calculates the gradient penalty loss for WGAN GP"""
    # 计算WGAN GP的梯度惩罚损失
    # Random weight term for interpolation between real and fake samples
    # 用于真实和虚假样本之间插值的随机权重项
    # alpha = Tensor(np.random.random((real_samples.size(0), 1)))
    alpha = Tensor(np.random.random((real_samples.size(0), 1, 1, 1)), device=real_samples.device)
    # Get random interpolation between real and fake samples
    # 获得真实和虚假样本之间的随机插值
    interpolates = (alpha * real_samples + ((1 - alpha) * fake_samples)).requires_grad_(True)
    d_interpolates = D(interpolates)
    # fake = Variable(Tensor(real_samples.shape[0], 1).fill_(1.0), requires_grad=False)
    fake = Variable(Tensor(real_samples.shape[0], d_interpolates.shape[1], device=real_samples.device).fill_(1.0), requires_grad=False)

    # Get gradient w.r.t. interpolates
    # 获得梯度，并进行内插
    gradients = autograd.grad(
        outputs=d_interpolates,
        inputs=interpolates,
        grad_outputs=fake,
        create_graph=True,
        retain_graph=True,
        only_inputs=True,
    )[0]
    gradients = gradients.view(gradients.size(0), -1)
    gradient_penalty = ((gradients.norm(2, dim=1) - 1) ** 2).mean()
    return gradient_penalty

def compute_gradient_penalty_real(D, real_samples):
    """Calculates the gradient penalty loss for WGAN GP"""
    # 计算WGAN GP的梯度惩罚损失
    real_samples.requires_grad_(True)
    d_real_samples = D(real_samples)

    fake = Variable(Tensor(real_samples.shape[0], d_real_samples.shape[1], device=real_samples.device).fill_(1.0), requires_grad=False)

    # Get gradient w.r.t. real_samples
    # 获得梯度
    gradients = autograd.grad(
        outputs=d_real_samples,
        inputs=real_samples,
        grad_outputs=fake,
        create_graph=True,
        retain_graph=True,
        only_inputs=True,
    )[0]
    gradients = gradients.view(gradients.size(0), -1)
    gradient_penalty = ((gradients.norm(2, dim=1))-1 ** 2).mean()

    return gradient_penalty

import torch.nn.functional as F

shadow_model = ModelMoCo(
        dim=128,
        K=4096,
        m=0.99,
        T=0.1,
        arch="resnet18",
        bn_splits=8,
        symmetric='store_true',
        ).to(device)
shadow_model = shadow_model.to(device)
# shadow_model.eval()
# test_acc_1 = test(shadow_model.encoder_q, memory_sub_loader, test_loader, memory_data_shadow_targets)
# 设置超参数
num_epochs = 1600
batch_size = 512
lr = 0.01
lambda_gp = 10


# 损失函数和优化器
loss_fn = nn.CrossEntropyLoss()
# optimizer = optim.SGD(shadow_model.parameters(), lr=lr, momentum=0.9, weight_decay=5e-4)
optimizer = optim.Adam(shadow_model.parameters(), lr=lr)

shadow_model.encoder_q.to(device)

# # 训练和测试循环
# for epoch in range(num_epochs):
#     # 训练阶段
#     shadow_model.train()
#     for i, (img_1,img_2) in enumerate(training_moco_loader_target):
#         img_1, img_2 = img_1.to(device), img_2.to(device)
#         optimizer.zero_grad()
#         loss_con = shadow_model(img_1,img_2)   
#         loss =  loss_con 
#         loss.backward()
#         optimizer.step()
        
#     if (epoch+1) % 100 == 0:
#         shadow_model.encoder_q.eval()
#         test_acc_1 = test(shadow_model.encoder_q, memory_sub_loader, test_loader, memory_data_shadow_targets)
#         print(epoch,"/",num_epochs)

# torch.save({'state_dict': shadow_model.state_dict()}, './save_model/'+'Target_cifar100_Adam_overfitting_1600epoch.pth')

import torch
import torch.nn as nn
import copy
import numpy as np
from torchvision import datasets, transforms
from torch.utils.data import DataLoader
shadow_model = ModelMoCo(
        dim=128,
        K=4096,
        m=0.99,
        T=0.1,
        arch="resnet18",
        bn_splits=8,
        symmetric='store_true',
        ).to(device)
checkpoint = torch.load('/home/<USER>/cnntest/machine_unlearning/save_model/cifar100_Adam_overfitting_1600epoch.pth', map_location="cpu")
state_dict = checkpoint['state_dict']
shadow_model.load_state_dict(state_dict, strict=False) 
shadow_model = shadow_model.to(device)

data_x, data_y = MIA_data(shadow_model.encoder_q, shadowin[0], shadowout[0])
_,__ = train_attacker(data_x, data_y)
# shadow_model.eval()
# test_acc_1 = test(shadow_model.encoder_q, memory_sub_loader, test_loader, memory_data_shadow_targets)

# model = ModelMoCo(
#         dim=128,
#         K=4096,
#         m=0.99,
#         T=0.1,
#         arch="resnet18",
#         bn_splits=8,
#         symmetric='store_true',
#         ).to(device)
# checkpoint = torch.load('/home/<USER>/cnntest/machine_unlearning/save_model/cifar100resnet18-test2-unlearning.pth', map_location="cpu")
# state_dict = checkpoint['state_dict']
# model.load_state_dict(state_dict, strict=False) 
# model = model.to(device)
# data_x, data_y = MIA_data(model.encoder_q, shadowin[0], shadowout[0])
# _,__ = train_attacker(data_x, data_y)
# model.eval()
# test_acc_1 = test(model.encoder_q, memory_sub_loader, test_loader, memory_data_shadow_targets)


# import torch
# import numpy as np
# import matplotlib.pyplot as plt
# from sklearn.manifold import TSNE
# from torchvision.models import resnet50
# from torchvision.datasets import CIFAR10
# from torchvision.transforms import ToTensor
# from torch.utils.data import DataLoader
# from sklearn.cluster import KMeans
# # Extract data from memory_sub_loader
# data_subset = []
# labels_subset = []

# for img, label in memory_sub_loader:
#     data_subset.extend(img)
#     labels_subset.extend(label)

# # Convert data to the format required by the model
# data_subset = torch.stack(data_subset)

# # Encode the data using the MoCo encoder
# with torch.no_grad():
#     device = next(shadow_model.parameters()).device
#     encoded_data = shadow_model.encoder_q(data_subset.to(device)).cpu().numpy()


# # Perform clustering on the encoded data
# num_clusters = 100  # Number of clusters (classes) in CIFAR-10
# kmeans = KMeans(n_clusters=num_clusters, random_state=42)
# cluster_labels = kmeans.fit_predict(encoded_data)

# # Perform t-SNE dimensionality reduction
# tsne = TSNE(n_components=2, random_state=42)
# reduced_data = tsne.fit_transform(encoded_data)

# # Plot the 2D representation of the dataset with cluster labels
# plt.figure(figsize=(13, 10))
# for label in np.unique(cluster_labels):
#     plt.scatter(reduced_data[cluster_labels == label, 0], reduced_data[cluster_labels == label, 1], label=label, alpha=0.6)
# plt.legend()
# plt.title("CIFAR-10 dataset visualization using MoCo encoder, t-SNE, and K-means clustering")
# plt.xlabel("Dimension 1")
# plt.ylabel("Dimension 2")
# plt.show()


import torch.nn.functional as F
from torch.optim.lr_scheduler import CosineAnnealingLR



shadow_model.eval()
test_acc_1 = test(shadow_model.encoder_q, ACC_loader, test_loader, ACC_shadow_targets)
# 设置超参数
num_epochs = 30
batch_size = 512
lr = 0.01
lambda_gp = 2


# 损失函数和优化器
loss_fn = nn.CrossEntropyLoss()
# optimizer = optim.SGD(shadow_model.parameters(), lr=lr, momentum=0.9, weight_decay=5e-4)
optimizer = optim.Adam(shadow_model.parameters(), lr=0.01)
# optimizer = optim.Adam(model.parameters(), lr=lr)
# optimizer = optim.Adam(shadow_model.parameters(), lr=lr)

shadow_model.encoder_q.to(device)
# scheduler = CosineAnnealingLR(optimizer=optimizer, T_max=num_epochs)
# 训练和测试循环
for epoch in range(num_epochs):
    # 训练阶段
    shadow_model.train()
    for i, ((img_t, _), (img_o,_),(imgi_1,imgi_2),(imgo_1,imgo_2)) in enumerate(zip(memory_sub_loader, test_sub_loader,train_sub_loader,out_loader)):
        # img_t1, img_t2 = img_t1.to(device), img_t2.to(device)
        # img_o1, img_o2 = img_o1.to(device), img_o2.to(device)
        img_o, img_t = img_o.to(device), img_t.to(device)
        imgi_1,imgi_2 = imgi_1.to(device),imgi_2.to(device)
        imgo_1,imgo_2 = imgo_1.to(device),imgo_2.to(device)
        optimizer.zero_grad()

        loss_con = shadow_model(imgi_1,imgi_2)
        mem_output = shadow_model.encoder_q(img_o, layer_name='avgpool')
        pred1 = F.softmax(mem_output)
        pred1, _ = torch.sort(pred1, descending=True)
        # treat third_pred as False flag
        nonmem_output = shadow_model.encoder_q(img_t, layer_name='avgpool')
        pred2 = F.softmax(nonmem_output)
        pred2, _ = torch.sort(pred2, descending=True)     
        # Gradient penalty
        # gradient_penalty = compute_gradient_penalty(shadow_model.encoder_q, img_o.data, img_t.data)
        gradient_penalty = compute_gradient_penalty_real(shadow_model.encoder_q, img_t.data)
        loss_sim = loss_fn(pred2,pred1.detach())
        # # Adversarial loss
        # # loss = -torch.mean(real_validity) + torch.mean(fake_validity) + lambda_gp * gradient_penalty + loss_con
        # print((100-test_acc_1),lambda_gp * gradient_penalty)
        # loss =  lambda_gp * gradient_penalty + loss_sim + loss_con + nn.KLDivLoss()(fake_validity, real_validity)*10
        # loss =  lambda_gp * gradient_penalty + nn.KLDivLoss()(pred2, pred1)*5 + loss_con + nn.KLDivLoss()(fake_validity, real_validity)*10
        # params = torch.cat([p.view(-1) for p in shadow_model.encoder_q.parameters()])

        # l1_regularization = torch.norm(params, p=1)
        # l2_regularization = torch.norm(params, p=2)


        loss =  gradient_penalty
        
        loss.backward()
        optimizer.step()
        print(gradient_penalty.item(),"loss:",loss.item()) 
       
    if((epoch+1)%3 == 0):
        shadow_model.encoder_q.eval()
        test_acc_1 = test(shadow_model.encoder_q, ACC_loader, test_loader, ACC_shadow_targets)
        print(epoch,"/",num_epochs)

torch.save({'state_dict': shadow_model.state_dict()}, './save_model/'+'cifar100'+'resnet18-test2-unlearning.pth')



# acc = []
# for i in range(10):
#     data_x, data_y = MIA_data(shadow_model.encoder_q, shadowin[0], shadowout[0])
#     train_acc,test_acc = train_attacker(data_x, data_y)
#     acc.append(test_acc)
# print(sum(acc) / len(acc))
data_x, data_y = MIA_data(shadow_model.encoder_q, shadowin[0], shadowout[0])
train_acc,test_acc = train_attacker(data_x, data_y)

import matplotlib.pyplot as plt
import numpy as np
import torch
device = torch.device("cuda:3" if torch.cuda.is_available() else "cpu")

origin_model = ModelMoCo(
        dim=128,
        K=4096,
        m=0.99,
        T=0.1,
        arch="resnet18",
        bn_splits=8,
        symmetric='store_true',
        ).to(device)
checkpoint = torch.load('/home/<USER>/cnntest/machine_unlearning/save_model/cifar100_Adam_overfitting_1600epoch.pth', map_location="cpu")
state_dict = checkpoint['state_dict']
origin_model.load_state_dict(state_dict, strict=False) 
origin_model = origin_model.to(device)


origin_model = origin_model.to(device)
shadow_model = shadow_model.to(device)
for i, ((img_t, _), (img_o,_),(imgi_1,imgi_2),(imgo_1,imgo_2)) in enumerate(zip(memory_sub_loader, test_sub_loader,train_sub_loader,out_loader)):
    imgi_1, imgi_2 = imgi_1.to(device), imgi_2.to(device)
    imgo_1, imgo_2 = imgo_1.to(device), imgo_2.to(device)
    img_o, img_t = img_o.to(device), img_t.to(device)

    per_sample_losses_before = origin_model.compute_loss_per_sample(imgi_1, imgi_2)
    per_sample_losses_after = shadow_model.compute_loss_per_sample(imgi_1, imgi_2)

    per_sample_losses_out_before = origin_model.compute_loss_per_sample(imgo_1, imgo_2)
    per_sample_losses_out_after = shadow_model.compute_loss_per_sample(imgo_1, imgo_2)
# 构造数据
tensor1 = per_sample_losses_before
tensor2 = per_sample_losses_after

# 转换成numpy数组
data1 = tensor1.detach().cpu().numpy()
data2 = tensor2.detach().cpu().numpy()

# 绘制散点图
plt.scatter(range(len(data1)), data1, label='member unlearn before')
plt.scatter(range(len(data2)), data2, label='nonmember unlearn before')

plt.legend()
plt.show()


tensor3 = per_sample_losses_out_before
tensor4 = per_sample_losses_out_after
data3 = tensor3.detach().cpu().numpy()
data4 = tensor4.detach().cpu().numpy()
plt.scatter(range(len(data3)), data3, label='member unlearn after')
plt.scatter(range(len(data4)), data4, label='nonmember unlearn after')

data3 = tensor3.detach().cpu().numpy()
data4 = tensor4.detach().cpu().numpy()
plt.legend()
plt.show()


device = torch.device("cuda:1" if torch.cuda.is_available() else "cpu")
# 加载预训练的 MoCo 模型
origin_model = origin_model.to(device)

# Move the images to the correct device
shadowin_im1,shadowin_im2 = shadowin
shadowout_im1,shadowout_im2 = shadowout

shadowin_data = DataLoader(shadowin, batch_size=512, shuffle=True, num_workers=16, pin_memory=True, drop_last=True)
shadowout_data = DataLoader(shadowout, batch_size=512, shuffle=True, num_workers=16, pin_memory=True, drop_last=True)


for i, ((shadowin_im1,shadowin_im2),(shadowout_im1,shadowout_im2)) in enumerate(zip(train_sub_loader,out_loader)):
    shadowin_im1 = shadowin_im1.to(device)
    shadowin_im2 = shadowin_im2.to(device)
    shadowout_im1 = shadowout_im1.to(device)
    shadowout_im2 = shadowout_im2.to(device)


shadowin_before1 = origin_model.encoder_q(shadowin_im1)
shadowin_before2 = origin_model.encoder_q(shadowin_im2)

normalized_im1 = F.normalize(shadowin_before1, dim=1)
normalized_im2 = F.normalize(shadowin_before2, dim=1)

# 计算两个归一化特征向量之间的点积（即余弦相似度）
cosine_similarity_mem_before = torch.sum(normalized_im1 * normalized_im2, dim=1)


shadowout_before1 = origin_model.encoder_q(shadowout_im1)
shadowout_before2 = origin_model.encoder_q(shadowout_im2)

normalized_im1 = F.normalize(shadowout_before1, dim=1)
normalized_im2 = F.normalize(shadowout_before2, dim=1)

# 计算两个归一化特征向量之间的点积（即余弦相似度）
cosine_similarity_nonmem_before = torch.sum(normalized_im1 * normalized_im2, dim=1)

# print(cosine_similarity2,cosine_similarity3)
print(cosine_similarity_mem_before.mean(),cosine_similarity_nonmem_before.mean())


#machine unlearning after
device = torch.device("cuda:3" if torch.cuda.is_available() else "cpu")
# 加载预训练的 MoCo 模型
moco_model = shadow_model.to(device)
for i, ((shadowin_im1,shadowin_im2),(shadowout_im1,shadowout_im2)) in enumerate(zip(train_sub_loader,out_loader)):
    shadowin_im1 = shadowin_im1.to(device)
    shadowin_im2 = shadowin_im2.to(device)
    shadowout_im1 = shadowout_im1.to(device)
    shadowout_im2 = shadowout_im2.to(device)

shadowin_im1 = moco_model.encoder_q(shadowin_im1)
shadowin_im2 = moco_model.encoder_q(shadowin_im2)

normalized_im1 = F.normalize(shadowin_im1, dim=1)
normalized_im2 = F.normalize(shadowin_im2, dim=1)

# 计算两个归一化特征向量之间的点积（即余弦相似度）
cosine_similarity2 = torch.sum(normalized_im1 * normalized_im2, dim=1)


shadowout_im1 = moco_model.encoder_q(shadowout_im1)
shadowout_im2 = moco_model.encoder_q(shadowout_im2)

normalized_im1 = F.normalize(shadowout_im1, dim=1)
normalized_im2 = F.normalize(shadowout_im2, dim=1)

# 计算两个归一化特征向量之间的点积（即余弦相似度）
cosine_similarity3 = torch.sum(normalized_im1 * normalized_im2, dim=1)

# print(cosine_similarity2,cosine_similarity3)
print(cosine_similarity2.mean(),cosine_similarity3.mean())

import matplotlib.pyplot as plt

x_labels = ['Member Before Avg', 'Non-member Before Avg', 'Member Aafter Avg', 'Non-member After Avg']
y_values = [cosine_similarity_mem_before.mean().item(), cosine_similarity_nonmem_before.mean().item(), cosine_similarity2.mean().item(), cosine_similarity3.mean().item()]

colors = ['green', 'green', 'orange', 'orange']
labels = ['Before Unlearning', 'Before Unlearning', 'After Unlearning', 'After Unlearning']

plt.figure(figsize=(10, 5))

# Add labels when plotting the bars
for i in range(len(x_labels)):
    plt.bar(x_labels[i], y_values[i], color=colors[i], label=labels[i])

plt.xlabel('Member and Nonmember')
plt.ylabel('Cosine similarity')
plt.title('CIFAR-10 Cosine Similarity Comparison')

# Remove duplicate labels in the legend
handles, labels = plt.gca().get_legend_handles_labels()
by_label = dict(zip(labels, handles))
plt.legend(by_label.values(), by_label.keys(), loc='upper right')

plt.show()



from sklearn.linear_model import LogisticRegression
from sklearn.tree import DecisionTreeClassifier
from sklearn.ensemble import RandomForestClassifier
from sklearn.neighbors import KNeighborsClassifier
from sklearn.discriminant_analysis import LinearDiscriminantAnalysis
import numpy as np
import numpy as np
import torch
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from sklearn.model_selection import train_test_split
from sklearn.svm import SVC
from sklearn.linear_model import LogisticRegression
from sklearn.discriminant_analysis import LinearDiscriminantAnalysis
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.svm import SVC
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import accuracy_score, precision_score, recall_score

# 假设你已经分别计算了成员数据和非成员数据的损失和余弦相似度，将它们存储在列表中
# member_loss = [1.2, 2.1, 1.5, 1.1]
# member_cosine_similarity = [0.9, 0.7, 0.8, 0.6]

# non_member_loss = [0.8, 0.5, 0.9, 0.6]
# non_member_cosine_similarity = [0.2, 0.1, 0.3, 0.4]
member_loss = per_sample_losses_before
member_cosine_similarity = cosine_similarity_mem_before
non_member_loss = per_sample_losses_out_before
non_member_cosine_similarity = cosine_similarity_nonmem_before
# 生成二维数据集
print(non_member_cosine_similarity.min(),member_cosine_similarity.min())
# 假设你的PyTorch张量在GPU上
member_loss_tensor = torch.tensor(member_loss).to('cuda:3')
member_cosine_similarity_tensor = torch.tensor(member_cosine_similarity).to('cuda:3')

# 首先将张量转移到CPU上，然后转换为NumPy数组
member_loss_numpy = member_loss_tensor.cpu().numpy()
member_cosine_similarity_numpy = member_cosine_similarity_tensor.cpu().numpy()


# 假设你的PyTorch张量在GPU上
non_member_loss_tensor = torch.tensor(non_member_loss).to('cuda:3')
non_member_cosine_similarity_tensor = torch.tensor(non_member_cosine_similarity).to('cuda:3')

# 首先将张量转移到CPU上，然后转换为NumPy数组
non_member_loss_numpy = non_member_loss_tensor.cpu().numpy()
non_member_cosine_similarity_numpy = non_member_cosine_similarity_tensor.cpu().numpy()

member_data = np.column_stack((member_loss_numpy, member_cosine_similarity_numpy, data_x[:512]))
non_member_data = np.column_stack((non_member_loss_numpy, non_member_cosine_similarity_numpy, data_x[512:1024]))

# 标签：成员数据的标签为1，非成员数据的标签为0
member_labels = [1] * len(member_data)
non_member_labels = [0] * len(non_member_data)

# 合并成员和非成员数据及其标签
data = np.concatenate((member_data, non_member_data), axis=0)
labels = member_labels + non_member_labels

# 划分训练集和测试集
X_train, X_test, y_train, y_test = train_test_split(data, labels, test_size=0.1, random_state=42)

# 定义分类器
classifiers = {
    "SVM": SVC(kernel='linear', C=1, probability=True),
    "Logistic Regression": LogisticRegression(),
    "LDA": LinearDiscriminantAnalysis()
}

best_classifier = None
best_accuracy = 0

# # 训练并评估多个分类器
# for name, classifier in classifiers.items():
#     classifier.fit(X_train, y_train)
#     accuracy = classifier.score(X_test, y_test)
#     print(f"{name} 测试集准确率: {accuracy * 100:.2f}%")

#     if accuracy > best_accuracy:
#         best_classifier = classifier
#         best_accuracy = accuracy

# print(f"\n最佳分类器: {best_classifier.__class__.__name__}")
# print(f"最佳分类器测试集准确率: {best_accuracy * 100:.2f}%")

# 训练并评估多个分类器
for name, classifier in classifiers.items():
    classifier.fit(X_train, y_train)
    y_pred = classifier.predict(X_test)
    accuracy = accuracy_score(y_test, y_pred)
    precision = precision_score(y_test, y_pred)
    recall = recall_score(y_test, y_pred)

    print(f"{name} 测试集准确率: {accuracy * 100:.2f}%")
    print(f"{name} 测试集精确度: {precision * 100:.2f}%")
    print(f"{name} 测试集召回率: {recall * 100:.2f}%")
    print()

    if accuracy > best_accuracy:
        best_classifier = classifier
        best_accuracy = accuracy

print(f"\n最佳分类器: {best_classifier.__class__.__name__}")
print(f"最佳分类器测试集准确率: {best_accuracy * 100:.2f}%")


# 创建3D散点图
loss = X_train[:, 0]
cosine_similarity = X_train[:, 1]
top_confidence = X_train[:, 2]
colors = ['green' if label == 1 else 'orange' for label in y_train]

fig = plt.figure()
ax = fig.add_subplot(111, projection='3d')
ax.scatter(loss, cosine_similarity, top_confidence, c=colors, marker='o', s=50)

# 设置轴标签
ax.set_xlabel('Loss')
ax.set_ylabel('Cosine Similarity')
ax.set_zlabel('Top Confidence')

# 更改视角
ax.view_init(30, 45)

# 定义图例
import matplotlib.patches as mpatches
green_patch = mpatches.Patch(color='green', label='Member')
orange_patch = mpatches.Patch(color='orange', label='Non-member')
plt.legend(handles=[green_patch, orange_patch])

plt.show()

from sklearn.linear_model import LogisticRegression
from sklearn.tree import DecisionTreeClassifier
from sklearn.ensemble import RandomForestClassifier
from sklearn.neighbors import KNeighborsClassifier
from sklearn.discriminant_analysis import LinearDiscriminantAnalysis
import numpy as np
import numpy as np
import torch
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from sklearn.model_selection import train_test_split
from sklearn.svm import SVC
from sklearn.linear_model import LogisticRegression
from sklearn.discriminant_analysis import LinearDiscriminantAnalysis
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.svm import SVC
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import accuracy_score, precision_score, recall_score

# 假设你已经分别计算了成员数据和非成员数据的损失和余弦相似度，将它们存储在列表中
# member_loss = [1.2, 2.1, 1.5, 1.1]
# member_cosine_similarity = [0.9, 0.7, 0.8, 0.6]

# non_member_loss = [0.8, 0.5, 0.9, 0.6]
# non_member_cosine_similarity = [0.2, 0.1, 0.3, 0.4]
member_loss = per_sample_losses_after
member_cosine_similarity = cosine_similarity2
non_member_loss = per_sample_losses_out_after
non_member_cosine_similarity = cosine_similarity3
# 生成二维数据集
print(non_member_cosine_similarity.min(),member_cosine_similarity.min())
# 假设你的PyTorch张量在GPU上
member_loss_tensor = torch.tensor(member_loss).to('cuda:3')
member_cosine_similarity_tensor = torch.tensor(member_cosine_similarity).to('cuda:3')

# 首先将张量转移到CPU上，然后转换为NumPy数组
member_loss_numpy = member_loss_tensor.cpu().numpy()
member_cosine_similarity_numpy = member_cosine_similarity_tensor.cpu().numpy()


# 假设你的PyTorch张量在GPU上
non_member_loss_tensor = torch.tensor(non_member_loss).to('cuda:3')
non_member_cosine_similarity_tensor = torch.tensor(non_member_cosine_similarity).to('cuda:3')

# 首先将张量转移到CPU上，然后转换为NumPy数组
non_member_loss_numpy = non_member_loss_tensor.cpu().numpy()
non_member_cosine_similarity_numpy = non_member_cosine_similarity_tensor.cpu().numpy()

member_data = np.column_stack((member_loss_numpy, member_cosine_similarity_numpy, data_x[:512]))
non_member_data = np.column_stack((non_member_loss_numpy, non_member_cosine_similarity_numpy, data_x[512:1024]))

# 标签：成员数据的标签为1，非成员数据的标签为0
member_labels = [1] * len(member_data)
non_member_labels = [0] * len(non_member_data)

# 合并成员和非成员数据及其标签
data = np.concatenate((member_data, non_member_data), axis=0)
labels = member_labels + non_member_labels

# 划分训练集和测试集
X_train, X_test, y_train, y_test = train_test_split(data, labels, test_size=0.1, random_state=42)

# 定义分类器
classifiers = {
    "SVM": SVC(kernel='linear', C=1, probability=True),
    "Logistic Regression": LogisticRegression(),
    "LDA": LinearDiscriminantAnalysis()
}

best_classifier = None
best_accuracy = 0

# # 训练并评估多个分类器
# for name, classifier in classifiers.items():
#     classifier.fit(X_train, y_train)
#     accuracy = classifier.score(X_test, y_test)
#     print(f"{name} 测试集准确率: {accuracy * 100:.2f}%")

#     if accuracy > best_accuracy:
#         best_classifier = classifier
#         best_accuracy = accuracy

# print(f"\n最佳分类器: {best_classifier.__class__.__name__}")
# print(f"最佳分类器测试集准确率: {best_accuracy * 100:.2f}%")

# 训练并评估多个分类器
for name, classifier in classifiers.items():
    classifier.fit(X_train, y_train)
    y_pred = classifier.predict(X_test)
    accuracy = accuracy_score(y_test, y_pred)
    precision = precision_score(y_test, y_pred)
    recall = recall_score(y_test, y_pred)

    print(f"{name} 测试集准确率: {accuracy * 100:.2f}%")
    print(f"{name} 测试集精确度: {precision * 100:.2f}%")
    print(f"{name} 测试集召回率: {recall * 100:.2f}%")
    print()

    if accuracy > best_accuracy:
        best_classifier = classifier
        best_accuracy = accuracy

print(f"\n最佳分类器: {best_classifier.__class__.__name__}")
print(f"最佳分类器测试集准确率: {best_accuracy * 100:.2f}%")


# 创建3D散点图
loss = X_train[:, 0]
cosine_similarity = X_train[:, 1]
top_confidence = X_train[:, 2]
colors = ['green' if label == 1 else 'orange' for label in y_train]

fig = plt.figure()
ax = fig.add_subplot(111, projection='3d')
ax.scatter(loss, cosine_similarity, top_confidence, c=colors, marker='o', s=50)

# 设置轴标签
ax.set_xlabel('Loss')
ax.set_ylabel('Cosine Similarity')
ax.set_zlabel('Top Confidence')

# 更改视角
ax.view_init(30, 45)

# 定义图例
import matplotlib.patches as mpatches
green_patch = mpatches.Patch(color='green', label='Member')
orange_patch = mpatches.Patch(color='orange', label='Non-member')
plt.legend(handles=[green_patch, orange_patch])

plt.show()
