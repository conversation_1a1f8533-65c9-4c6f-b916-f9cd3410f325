"""
Script to run Membership Inference Attack (MIA) on trained models
"""

import os
import sys
import torch
import logging

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from models.moco import ModelMoCo
from data.dataset import create_data_loaders, save_mia_data, load_mia_data
from attacks.mia import MIA_data, train_attacker
from utils.config import load_config, setup_reproducibility, setup_logging


def load_trained_model(config, model_path, model_type='target'):
    """
    Load a trained MoCo model
    
    Args:
        config: Configuration object
        model_path: Path to the saved model
        model_type: Type of model ('target' or 'shadow')
        
    Returns:
        Loaded model
    """
    logger = logging.getLogger(__name__)
    
    # Create model
    model = ModelMoCo(
        dim=config.model.moco.dim,
        K=config.model.moco.K,
        m=config.model.moco.m,
        T=config.model.moco.T,
        arch=config.model.moco.arch,
        bn_splits=config.model.moco.bn_splits,
        symmetric=config.model.moco.symmetric
    ).to(config.device)
    
    # Load state dict
    if os.path.exists(model_path):
        checkpoint = torch.load(model_path, map_location=config.device)
        model.load_state_dict(checkpoint['state_dict'])
        logger.info(f"Loaded {model_type} model from {model_path}")
    else:
        logger.error(f"Model file not found: {model_path}")
        raise FileNotFoundError(f"Model file not found: {model_path}")
    
    return model


def prepare_mia_data(config, data_loaders):
    """
    Prepare data for MIA attack
    
    Args:
        config: Configuration object
        data_loaders: Dictionary of data loaders
    """
    logger = logging.getLogger(__name__)
    
    # Check if MIA data already exists
    train_data_path = os.path.join(config.paths.logs_dir, 'cifar100train.npz')
    test_data_path = os.path.join(config.paths.logs_dir, 'cifar100test.npz')
    
    if os.path.exists(train_data_path) and os.path.exists(test_data_path):
        logger.info("MIA data files already exist, loading from disk...")
        shadowin, shadowout = load_mia_data(config.paths.logs_dir)
    else:
        logger.info("Preparing MIA data...")
        save_mia_data(
            data_loaders['mia_train'], 
            data_loaders['mia_test'], 
            config.paths.logs_dir
        )
        shadowin, shadowout = load_mia_data(config.paths.logs_dir)
    
    return shadowin, shadowout


def run_mia_attack(config, shadow_model_path, target_model_path=None):
    """
    Run MIA attack on the target model using shadow model
    
    Args:
        config: Configuration object
        shadow_model_path: Path to the shadow model
        target_model_path: Path to the target model (optional)
    """
    logger = logging.getLogger(__name__)
    
    # Create data loaders
    logger.info("Creating data loaders...")
    data_loaders = create_data_loaders(config)
    
    # Prepare MIA data
    shadowin, shadowout = prepare_mia_data(config, data_loaders)
    
    # Load shadow model
    logger.info("Loading shadow model...")
    shadow_model = load_trained_model(config, shadow_model_path, 'shadow')
    shadow_model.eval()
    
    # Generate MIA attack data using shadow model
    logger.info("Generating MIA attack data...")
    in_data, in_labels = shadowin
    out_data, out_labels = shadowout
    
    # Generate attack features
    attack_data, attack_labels = MIA_data(
        shadow_model.encoder_q, 
        in_data, 
        out_data, 
        config.device
    )
    
    # Train MIA attack model
    logger.info("Training MIA attack model...")
    attack_model_path = os.path.join(config.paths.save_model_dir, config.paths.attack_model_file)
    train_acc, test_acc = train_attacker(
        attack_data, 
        attack_labels, 
        attack_model_path, 
        config.data.test_split
    )
    
    logger.info(f"MIA Attack Results:")
    logger.info(f"Training Accuracy: {train_acc:.4f}")
    logger.info(f"Testing Accuracy: {test_acc:.4f}")
    
    # If target model is provided, evaluate attack on target model
    if target_model_path and os.path.exists(target_model_path):
        logger.info("Evaluating attack on target model...")
        target_model = load_trained_model(config, target_model_path, 'target')
        target_model.eval()
        
        # Generate attack data for target model
        target_attack_data, target_attack_labels = MIA_data(
            target_model.encoder_q, 
            in_data, 
            out_data, 
            config.device
        )
        
        # Load trained attack model and evaluate
        from attacks.mia import load_attack_model, evaluate_mia_attack
        attack_model = load_attack_model(attack_model_path)
        
        logger.info("Target model attack results:")
        target_acc, target_auc = evaluate_mia_attack(
            attack_model, 
            target_attack_data, 
            target_attack_labels
        )
    
    return train_acc, test_acc


def main():
    """Main function"""
    # Parse command line arguments
    import argparse
    parser = argparse.ArgumentParser(description='Run MIA attack')
    parser.add_argument('--shadow_model', type=str, required=True,
                       help='Path to shadow model')
    parser.add_argument('--target_model', type=str, default=None,
                       help='Path to target model (optional)')
    parser.add_argument('--config', type=str, default=None,
                       help='Path to configuration file')
    
    args = parser.parse_args()
    
    # Load configuration
    config = load_config(args.config)
    
    # Setup reproducibility
    setup_reproducibility(config)
    
    # Setup logging
    logger = setup_logging(config)
    
    # Print configuration
    config.print_config()
    
    # Run MIA attack
    logger.info("Starting MIA attack...")
    train_acc, test_acc = run_mia_attack(config, args.shadow_model, args.target_model)
    
    logger.info("MIA attack completed successfully!")
    logger.info(f"Final Results - Train Acc: {train_acc:.4f}, Test Acc: {test_acc:.4f}")


if __name__ == '__main__':
    main()
