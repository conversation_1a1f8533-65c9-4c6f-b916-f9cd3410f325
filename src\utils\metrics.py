"""
Metrics calculation utilities for MIA experiments
"""

import numpy as np
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    roc_auc_score, roc_curve, precision_recall_curve,
    confusion_matrix, classification_report
)
import matplotlib.pyplot as plt


def calculate_mia_metrics(y_true, y_pred, y_pred_proba=None):
    """
    Calculate comprehensive metrics for MIA attack evaluation
    
    Args:
        y_true: True labels (1 for members, 0 for non-members)
        y_pred: Predicted labels
        y_pred_proba: Predicted probabilities (optional)
        
    Returns:
        Dictionary containing all metrics
    """
    metrics = {}
    
    # Basic classification metrics
    metrics['accuracy'] = accuracy_score(y_true, y_pred)
    metrics['precision'] = precision_score(y_true, y_pred)
    metrics['recall'] = recall_score(y_true, y_pred)
    metrics['f1_score'] = f1_score(y_true, y_pred)
    
    # Confusion matrix
    cm = confusion_matrix(y_true, y_pred)
    metrics['confusion_matrix'] = cm
    
    # True/False Positive/Negative rates
    tn, fp, fn, tp = cm.ravel()
    metrics['true_positive_rate'] = tp / (tp + fn) if (tp + fn) > 0 else 0
    metrics['false_positive_rate'] = fp / (fp + tn) if (fp + tn) > 0 else 0
    metrics['true_negative_rate'] = tn / (tn + fp) if (tn + fp) > 0 else 0
    metrics['false_negative_rate'] = fn / (fn + tp) if (fn + tp) > 0 else 0
    
    # MIA-specific metrics
    metrics['attack_advantage'] = metrics['true_positive_rate'] - metrics['false_positive_rate']
    
    # Probability-based metrics (if probabilities are provided)
    if y_pred_proba is not None:
        metrics['auc_roc'] = roc_auc_score(y_true, y_pred_proba)
        
        # Calculate ROC curve
        fpr, tpr, roc_thresholds = roc_curve(y_true, y_pred_proba)
        metrics['roc_curve'] = {'fpr': fpr, 'tpr': tpr, 'thresholds': roc_thresholds}
        
        # Calculate Precision-Recall curve
        precision, recall, pr_thresholds = precision_recall_curve(y_true, y_pred_proba)
        metrics['pr_curve'] = {'precision': precision, 'recall': recall, 'thresholds': pr_thresholds}
        
        # Calculate AUC for PR curve
        metrics['auc_pr'] = np.trapz(precision, recall)
    
    # Classification report
    metrics['classification_report'] = classification_report(y_true, y_pred, output_dict=True)
    
    return metrics


def print_mia_metrics(metrics):
    """
    Print MIA metrics in a formatted way
    
    Args:
        metrics: Dictionary containing metrics from calculate_mia_metrics
    """
    print("=" * 60)
    print("MIA ATTACK EVALUATION METRICS")
    print("=" * 60)
    
    print(f"Accuracy:           {metrics['accuracy']:.4f}")
    print(f"Precision:          {metrics['precision']:.4f}")
    print(f"Recall:             {metrics['recall']:.4f}")
    print(f"F1-Score:           {metrics['f1_score']:.4f}")
    
    if 'auc_roc' in metrics:
        print(f"AUC-ROC:            {metrics['auc_roc']:.4f}")
        print(f"AUC-PR:             {metrics['auc_pr']:.4f}")
    
    print(f"Attack Advantage:   {metrics['attack_advantage']:.4f}")
    
    print("\nConfusion Matrix:")
    print("                 Predicted")
    print("                Non-Member  Member")
    print(f"Actual Non-Member    {metrics['confusion_matrix'][0,0]:6d}    {metrics['confusion_matrix'][0,1]:6d}")
    print(f"Actual Member        {metrics['confusion_matrix'][1,0]:6d}    {metrics['confusion_matrix'][1,1]:6d}")
    
    print(f"\nTrue Positive Rate:  {metrics['true_positive_rate']:.4f}")
    print(f"False Positive Rate: {metrics['false_positive_rate']:.4f}")
    print(f"True Negative Rate:  {metrics['true_negative_rate']:.4f}")
    print(f"False Negative Rate: {metrics['false_negative_rate']:.4f}")
    
    print("=" * 60)


def plot_roc_curve(metrics, save_path=None):
    """
    Plot ROC curve
    
    Args:
        metrics: Dictionary containing ROC curve data
        save_path: Path to save the plot (optional)
    """
    if 'roc_curve' not in metrics:
        print("ROC curve data not available in metrics")
        return
    
    plt.figure(figsize=(8, 6))
    
    fpr = metrics['roc_curve']['fpr']
    tpr = metrics['roc_curve']['tpr']
    auc = metrics['auc_roc']
    
    plt.plot(fpr, tpr, 'b-', linewidth=2, label=f'ROC Curve (AUC = {auc:.3f})')
    plt.plot([0, 1], [0, 1], 'r--', linewidth=1, label='Random Classifier')
    
    plt.xlabel('False Positive Rate')
    plt.ylabel('True Positive Rate')
    plt.title('ROC Curve for MIA Attack')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"ROC curve saved to {save_path}")
    
    plt.show()


def plot_precision_recall_curve(metrics, save_path=None):
    """
    Plot Precision-Recall curve
    
    Args:
        metrics: Dictionary containing PR curve data
        save_path: Path to save the plot (optional)
    """
    if 'pr_curve' not in metrics:
        print("Precision-Recall curve data not available in metrics")
        return
    
    plt.figure(figsize=(8, 6))
    
    precision = metrics['pr_curve']['precision']
    recall = metrics['pr_curve']['recall']
    auc_pr = metrics['auc_pr']
    
    plt.plot(recall, precision, 'b-', linewidth=2, label=f'PR Curve (AUC = {auc_pr:.3f})')
    plt.axhline(y=0.5, color='r', linestyle='--', linewidth=1, label='Random Classifier')
    
    plt.xlabel('Recall')
    plt.ylabel('Precision')
    plt.title('Precision-Recall Curve for MIA Attack')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Precision-Recall curve saved to {save_path}")
    
    plt.show()


def plot_confusion_matrix(metrics, save_path=None):
    """
    Plot confusion matrix as a heatmap
    
    Args:
        metrics: Dictionary containing confusion matrix
        save_path: Path to save the plot (optional)
    """
    import seaborn as sns
    
    plt.figure(figsize=(8, 6))
    
    cm = metrics['confusion_matrix']
    
    # Create labels
    labels = ['Non-Member', 'Member']
    
    # Create heatmap
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                xticklabels=labels, yticklabels=labels,
                cbar_kws={'label': 'Count'})
    
    plt.xlabel('Predicted Label')
    plt.ylabel('True Label')
    plt.title('Confusion Matrix for MIA Attack')
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Confusion matrix saved to {save_path}")
    
    plt.show()


def compare_attack_methods(results_dict, metric='accuracy', save_path=None):
    """
    Compare different attack methods
    
    Args:
        results_dict: Dictionary with method names as keys and metrics as values
        metric: Metric to compare ('accuracy', 'auc_roc', 'f1_score', etc.)
        save_path: Path to save the plot (optional)
    """
    methods = list(results_dict.keys())
    values = [results_dict[method][metric] for method in methods]
    
    plt.figure(figsize=(10, 6))
    
    bars = plt.bar(methods, values, color=['skyblue', 'lightcoral', 'lightgreen', 'gold'][:len(methods)])
    
    # Add value labels on bars
    for bar, value in zip(bars, values):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{value:.3f}', ha='center', va='bottom', fontweight='bold')
    
    plt.xlabel('Attack Method')
    plt.ylabel(metric.replace('_', ' ').title())
    plt.title(f'Comparison of Attack Methods by {metric.replace("_", " ").title()}')
    plt.ylim(0, 1.1)
    plt.grid(True, alpha=0.3, axis='y')
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Comparison plot saved to {save_path}")
    
    plt.show()


def calculate_privacy_risk(metrics):
    """
    Calculate privacy risk based on MIA attack success
    
    Args:
        metrics: Dictionary containing MIA metrics
        
    Returns:
        Dictionary containing privacy risk assessment
    """
    risk_assessment = {}
    
    accuracy = metrics['accuracy']
    auc_roc = metrics.get('auc_roc', 0.5)
    attack_advantage = metrics['attack_advantage']
    
    # Risk levels based on attack success
    if accuracy >= 0.9 or auc_roc >= 0.9:
        risk_level = "CRITICAL"
        risk_score = 5
    elif accuracy >= 0.8 or auc_roc >= 0.8:
        risk_level = "HIGH"
        risk_score = 4
    elif accuracy >= 0.7 or auc_roc >= 0.7:
        risk_level = "MEDIUM"
        risk_score = 3
    elif accuracy >= 0.6 or auc_roc >= 0.6:
        risk_level = "LOW"
        risk_score = 2
    else:
        risk_level = "MINIMAL"
        risk_score = 1
    
    risk_assessment['risk_level'] = risk_level
    risk_assessment['risk_score'] = risk_score
    risk_assessment['attack_accuracy'] = accuracy
    risk_assessment['attack_auc'] = auc_roc
    risk_assessment['attack_advantage'] = attack_advantage
    
    # Recommendations
    recommendations = []
    if risk_score >= 4:
        recommendations.extend([
            "Consider implementing differential privacy",
            "Reduce model complexity or use regularization",
            "Implement data augmentation techniques"
        ])
    elif risk_score >= 3:
        recommendations.extend([
            "Monitor model for overfitting",
            "Consider ensemble methods",
            "Implement privacy-preserving techniques"
        ])
    else:
        recommendations.append("Current privacy risk is acceptable")
    
    risk_assessment['recommendations'] = recommendations
    
    return risk_assessment
