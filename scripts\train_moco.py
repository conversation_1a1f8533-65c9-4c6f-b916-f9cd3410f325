"""
Training script for MoCo model on CIFAR-100
"""

import os
import sys
import torch
import torch.optim as optim
from tqdm import tqdm
import logging

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from models.moco import ModelMoCo
from data.dataset import create_data_loaders
from utils.config import load_config, setup_reproducibility, setup_logging
from utils.evaluation import test


def train_moco_model(config, model_type='target'):
    """
    Train MoCo model
    
    Args:
        config: Configuration object
        model_type: Type of model to train ('target' or 'shadow')
    """
    logger = logging.getLogger(__name__)
    
    # Create data loaders
    logger.info("Creating data loaders...")
    data_loaders = create_data_loaders(config)
    
    # Select appropriate data loader based on model type
    if model_type == 'target':
        train_loader = data_loaders['target_train']
        model_save_name = config.paths.target_model_file
    elif model_type == 'shadow':
        train_loader = data_loaders['shadow_train_target']
        model_save_name = config.paths.shadow_model_file
    else:
        raise ValueError(f"Unknown model type: {model_type}")
    
    # Create model
    logger.info(f"Creating {model_type} MoCo model...")
    model = ModelMoCo(
        dim=config.model.moco.dim,
        K=config.model.moco.K,
        m=config.model.moco.m,
        T=config.model.moco.T,
        arch=config.model.moco.arch,
        bn_splits=config.model.moco.bn_splits,
        symmetric=config.model.moco.symmetric
    ).to(config.device)
    
    # Create optimizer
    if config.training.optimizer.lower() == 'adam':
        optimizer = optim.Adam(
            model.parameters(), 
            lr=config.training.learning_rate
        )
    elif config.training.optimizer.lower() == 'sgd':
        optimizer = optim.SGD(
            model.parameters(), 
            lr=config.training.learning_rate,
            momentum=config.training.momentum,
            weight_decay=config.training.weight_decay
        )
    else:
        raise ValueError(f"Unknown optimizer: {config.training.optimizer}")
    
    # Training loop
    logger.info(f"Starting training for {config.training.num_epochs} epochs...")
    model.train()
    
    for epoch in range(config.training.num_epochs):
        epoch_loss = 0.0
        num_batches = 0
        
        # Training phase
        train_bar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{config.training.num_epochs}')
        for i, (img_1, img_2) in enumerate(train_bar):
            img_1, img_2 = img_1.to(config.device), img_2.to(config.device)
            
            optimizer.zero_grad()
            loss = model(img_1, img_2, config.device)
            loss.backward()
            optimizer.step()
            
            epoch_loss += loss.item()
            num_batches += 1
            
            # Update progress bar
            train_bar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Avg Loss': f'{epoch_loss/num_batches:.4f}'
            })
        
        # Log epoch results
        avg_loss = epoch_loss / num_batches
        logger.info(f'Epoch {epoch+1}/{config.training.num_epochs}, Average Loss: {avg_loss:.4f}')
        
        # Evaluation
        if (epoch + 1) % config.training.eval_frequency == 0:
            logger.info("Evaluating model...")
            model.encoder_q.eval()
            test_acc = test(
                model.encoder_q, 
                data_loaders['eval_memory'], 
                data_loaders['test'], 
                data_loaders['memory_targets'],
                config.device,
                config.evaluation.classes,
                config.evaluation.knn_k,
                config.evaluation.knn_t
            )
            logger.info(f'Test Accuracy: {test_acc:.2f}%')
            model.train()
        
        # Save model checkpoint
        if (epoch + 1) % config.training.save_frequency == 0:
            save_path = os.path.join(config.paths.save_model_dir, f'{model_type}_checkpoint_epoch_{epoch+1}.pth')
            torch.save({'state_dict': model.state_dict(), 'epoch': epoch+1}, save_path)
            logger.info(f'Model checkpoint saved to {save_path}')
    
    # Save final model
    final_save_path = os.path.join(config.paths.save_model_dir, model_save_name)
    torch.save({'state_dict': model.state_dict()}, final_save_path)
    logger.info(f'Final model saved to {final_save_path}')
    
    return model


def main():
    """Main training function"""
    # Load configuration
    config = load_config()
    
    # Setup reproducibility
    setup_reproducibility(config)
    
    # Setup logging
    logger = setup_logging(config)
    
    # Print configuration
    config.print_config()
    
    # Parse command line arguments for model type
    import argparse
    parser = argparse.ArgumentParser(description='Train MoCo model')
    parser.add_argument('--model_type', type=str, default='target', 
                       choices=['target', 'shadow'],
                       help='Type of model to train (target or shadow)')
    parser.add_argument('--config', type=str, default=None,
                       help='Path to configuration file')
    
    args = parser.parse_args()
    
    # Load custom config if provided
    if args.config:
        config = load_config(args.config)
        setup_reproducibility(config)
        logger = setup_logging(config)
    
    # Train model
    logger.info(f"Training {args.model_type} model...")
    model = train_moco_model(config, args.model_type)
    
    logger.info("Training completed successfully!")


if __name__ == '__main__':
    main()
