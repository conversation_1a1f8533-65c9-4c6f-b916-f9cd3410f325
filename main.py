"""
Main entry point for CIFAR-100 Membership Inference Attack experiments
"""

import os
import sys
import argparse
import logging

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from utils.config import load_config, setup_reproducibility, setup_logging


def main():
    """Main function with command-line interface"""
    parser = argparse.ArgumentParser(
        description='CIFAR-100 Membership Inference Attack Experiments',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Train target model
  python main.py train --model_type target
  
  # Train shadow model
  python main.py train --model_type shadow
  
  # Run MIA attack
  python main.py attack --shadow_model ./save_model/shadow_model.pth
  
  # Run complete experiment pipeline
  python main.py pipeline
  
  # Evaluate existing models
  python main.py evaluate --model_path ./save_model/target_model.pth
        """
    )
    
    # Add subcommands
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Train command
    train_parser = subparsers.add_parser('train', help='Train MoCo models')
    train_parser.add_argument('--model_type', type=str, required=True,
                             choices=['target', 'shadow'],
                             help='Type of model to train')
    train_parser.add_argument('--config', type=str, default=None,
                             help='Path to configuration file')
    train_parser.add_argument('--resume', type=str, default=None,
                             help='Path to checkpoint to resume from')
    
    # Attack command
    attack_parser = subparsers.add_parser('attack', help='Run MIA attack')
    attack_parser.add_argument('--shadow_model', type=str, required=True,
                              help='Path to shadow model')
    attack_parser.add_argument('--target_model', type=str, default=None,
                              help='Path to target model (optional)')
    attack_parser.add_argument('--config', type=str, default=None,
                              help='Path to configuration file')
    
    # Pipeline command
    pipeline_parser = subparsers.add_parser('pipeline', help='Run complete experiment pipeline')
    pipeline_parser.add_argument('--config', type=str, default=None,
                                help='Path to configuration file')
    pipeline_parser.add_argument('--skip_training', action='store_true',
                                help='Skip training and use existing models')
    
    # Evaluate command
    eval_parser = subparsers.add_parser('evaluate', help='Evaluate trained models')
    eval_parser.add_argument('--model_path', type=str, required=True,
                            help='Path to model to evaluate')
    eval_parser.add_argument('--model_type', type=str, default='target',
                            choices=['target', 'shadow'],
                            help='Type of model')
    eval_parser.add_argument('--config', type=str, default=None,
                            help='Path to configuration file')
    
    # Parse arguments
    args = parser.parse_args()
    
    if args.command is None:
        parser.print_help()
        return
    
    # Load configuration
    config = load_config(args.config)
    
    # Setup reproducibility and logging
    setup_reproducibility(config)
    logger = setup_logging(config)
    
    # Print configuration
    config.print_config()
    
    # Execute command
    if args.command == 'train':
        run_training(args, config, logger)
    elif args.command == 'attack':
        run_attack(args, config, logger)
    elif args.command == 'pipeline':
        run_pipeline(args, config, logger)
    elif args.command == 'evaluate':
        run_evaluation(args, config, logger)


def run_training(args, config, logger):
    """Run model training"""
    logger.info(f"Starting training for {args.model_type} model...")
    
    # Import training script
    sys.path.append(os.path.join(os.path.dirname(__file__), 'scripts'))
    from train_moco import train_moco_model
    
    try:
        model = train_moco_model(config, args.model_type)
        logger.info(f"{args.model_type.capitalize()} model training completed successfully!")
    except Exception as e:
        logger.error(f"Training failed: {str(e)}")
        raise


def run_attack(args, config, logger):
    """Run MIA attack"""
    logger.info("Starting MIA attack...")
    
    # Import attack script
    sys.path.append(os.path.join(os.path.dirname(__file__), 'scripts'))
    from run_mia_attack import run_mia_attack
    
    try:
        train_acc, test_acc = run_mia_attack(config, args.shadow_model, args.target_model)
        logger.info("MIA attack completed successfully!")
        logger.info(f"Attack Results - Train Acc: {train_acc:.4f}, Test Acc: {test_acc:.4f}")
    except Exception as e:
        logger.error(f"MIA attack failed: {str(e)}")
        raise


def run_pipeline(args, config, logger):
    """Run complete experiment pipeline"""
    logger.info("Starting complete experiment pipeline...")
    
    try:
        if not args.skip_training:
            # Train shadow model
            logger.info("Step 1: Training shadow model...")
            run_training(type('Args', (), {'model_type': 'shadow', 'config': args.config, 'resume': None})(), config, logger)
            
            # Train target model
            logger.info("Step 2: Training target model...")
            run_training(type('Args', (), {'model_type': 'target', 'config': args.config, 'resume': None})(), config, logger)
        
        # Run MIA attack
        logger.info("Step 3: Running MIA attack...")
        shadow_model_path = os.path.join(config.paths.save_model_dir, config.paths.shadow_model_file)
        target_model_path = os.path.join(config.paths.save_model_dir, config.paths.target_model_file)
        
        attack_args = type('Args', (), {
            'shadow_model': shadow_model_path,
            'target_model': target_model_path,
            'config': args.config
        })()
        
        run_attack(attack_args, config, logger)
        
        logger.info("Complete experiment pipeline finished successfully!")
        
    except Exception as e:
        logger.error(f"Pipeline failed: {str(e)}")
        raise


def run_evaluation(args, config, logger):
    """Run model evaluation"""
    logger.info(f"Evaluating {args.model_type} model...")
    
    # Import evaluation utilities
    from models.moco import ModelMoCo
    from data.dataset import create_data_loaders
    from utils.evaluation import test
    import torch
    
    try:
        # Load model
        model = ModelMoCo(
            dim=config.model.moco.dim,
            K=config.model.moco.K,
            m=config.model.moco.m,
            T=config.model.moco.T,
            arch=config.model.moco.arch,
            bn_splits=config.model.moco.bn_splits,
            symmetric=config.model.moco.symmetric
        ).to(config.device)
        
        if os.path.exists(args.model_path):
            checkpoint = torch.load(args.model_path, map_location=config.device)
            model.load_state_dict(checkpoint['state_dict'])
            logger.info(f"Loaded model from {args.model_path}")
        else:
            logger.error(f"Model file not found: {args.model_path}")
            return
        
        # Create data loaders
        data_loaders = create_data_loaders(config)
        
        # Evaluate model
        model.encoder_q.eval()
        test_acc = test(
            model.encoder_q,
            data_loaders['eval_memory'],
            data_loaders['test'],
            data_loaders['memory_targets'],
            config.device,
            config.evaluation.classes,
            config.evaluation.knn_k,
            config.evaluation.knn_t
        )
        
        logger.info(f"Model evaluation completed!")
        logger.info(f"Test Accuracy: {test_acc:.2f}%")
        
    except Exception as e:
        logger.error(f"Evaluation failed: {str(e)}")
        raise


if __name__ == '__main__':
    main()
