"""
Visualization utilities for MIA experiments
"""

import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
from mpl_toolkits.mplot3d import Axes3D
import pandas as pd
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA


def plot_training_curves(train_losses, val_accuracies=None, save_path=None):
    """
    Plot training loss curves and validation accuracies
    
    Args:
        train_losses: List of training losses
        val_accuracies: List of validation accuracies (optional)
        save_path: Path to save the plot (optional)
    """
    fig, axes = plt.subplots(1, 2 if val_accuracies else 1, figsize=(15, 5))
    
    if val_accuracies is None:
        axes = [axes]
    
    # Plot training loss
    axes[0].plot(train_losses, 'b-', linewidth=2, label='Training Loss')
    axes[0].set_xlabel('Epoch')
    axes[0].set_ylabel('Loss')
    axes[0].set_title('Training Loss Curve')
    axes[0].grid(True, alpha=0.3)
    axes[0].legend()
    
    # Plot validation accuracy if provided
    if val_accuracies:
        axes[1].plot(val_accuracies, 'r-', linewidth=2, label='Validation Accuracy')
        axes[1].set_xlabel('Epoch')
        axes[1].set_ylabel('Accuracy (%)')
        axes[1].set_title('Validation Accuracy Curve')
        axes[1].grid(True, alpha=0.3)
        axes[1].legend()
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Training curves saved to {save_path}")
    
    plt.show()


def plot_mia_attack_results_3d(X_test, y_test, save_path=None):
    """
    Create a 3D scatter plot of MIA attack results
    
    Args:
        X_test: Test features (should have at least 3 dimensions)
        y_test: Test labels (1 for members, 0 for non-members)
        save_path: Path to save the plot (optional)
    """
    # Separate member and non-member data
    member_data = X_test[y_test == 1]
    non_member_data = X_test[y_test == 0]
    
    # Create 3D scatter plot
    fig = plt.figure(figsize=(12, 9))
    ax = fig.add_subplot(111, projection='3d')
    
    # Plot using first three probabilities
    scatter1 = ax.scatter(member_data[:, 0], member_data[:, 1], member_data[:, 2], 
                         c='red', label='Member', alpha=0.6, s=20)
    scatter2 = ax.scatter(non_member_data[:, 0], non_member_data[:, 1], non_member_data[:, 2], 
                         c='blue', label='Non-Member', alpha=0.6, s=20)
    
    ax.set_xlabel('First Probability', fontsize=12)
    ax.set_ylabel('Second Probability', fontsize=12)
    ax.set_zlabel('Third Probability', fontsize=12)
    ax.legend(fontsize=12)
    ax.set_title('Member vs Non-Member Data Distribution (3D)', fontsize=14)
    
    # Add grid
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"3D scatter plot saved to {save_path}")
    
    plt.show()


def plot_mia_attack_results_2d(X_test, y_test, save_path=None):
    """
    Create 2D scatter plots of MIA attack results
    
    Args:
        X_test: Test features
        y_test: Test labels
        save_path: Path to save the plot (optional)
    """
    # Create subplots for different probability combinations
    fig, axes = plt.subplots(1, 3, figsize=(18, 5))
    
    member_data = X_test[y_test == 1]
    non_member_data = X_test[y_test == 0]
    
    # Plot combinations of top 3 probabilities
    combinations = [(0, 1), (0, 2), (1, 2)]
    labels = ['First vs Second', 'First vs Third', 'Second vs Third']
    
    for i, (x_idx, y_idx) in enumerate(combinations):
        axes[i].scatter(member_data[:, x_idx], member_data[:, y_idx], 
                       c='red', label='Member', alpha=0.6, s=20)
        axes[i].scatter(non_member_data[:, x_idx], non_member_data[:, y_idx], 
                       c='blue', label='Non-Member', alpha=0.6, s=20)
        
        axes[i].set_xlabel(f'Probability {x_idx + 1}')
        axes[i].set_ylabel(f'Probability {y_idx + 1}')
        axes[i].set_title(f'{labels[i]} Probability')
        axes[i].legend()
        axes[i].grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"2D scatter plots saved to {save_path}")
    
    plt.show()


def plot_probability_distributions(X_test, y_test, save_path=None):
    """
    Plot probability distributions for members vs non-members
    
    Args:
        X_test: Test features
        y_test: Test labels
        save_path: Path to save the plot (optional)
    """
    fig, axes = plt.subplots(1, 3, figsize=(18, 5))
    
    member_data = X_test[y_test == 1]
    non_member_data = X_test[y_test == 0]
    
    for i in range(3):
        # Plot histograms
        axes[i].hist(member_data[:, i], bins=30, alpha=0.7, label='Member', 
                    color='red', density=True)
        axes[i].hist(non_member_data[:, i], bins=30, alpha=0.7, label='Non-Member', 
                    color='blue', density=True)
        
        axes[i].set_xlabel(f'Probability {i + 1}')
        axes[i].set_ylabel('Density')
        axes[i].set_title(f'Distribution of Probability {i + 1}')
        axes[i].legend()
        axes[i].grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Probability distributions saved to {save_path}")
    
    plt.show()


def plot_tsne_visualization(X_test, y_test, save_path=None):
    """
    Create t-SNE visualization of MIA attack features
    
    Args:
        X_test: Test features
        y_test: Test labels
        save_path: Path to save the plot (optional)
    """
    # Apply t-SNE
    tsne = TSNE(n_components=2, random_state=42, perplexity=30)
    X_tsne = tsne.fit_transform(X_test)
    
    # Create plot
    plt.figure(figsize=(10, 8))
    
    member_mask = y_test == 1
    non_member_mask = y_test == 0
    
    plt.scatter(X_tsne[member_mask, 0], X_tsne[member_mask, 1], 
               c='red', label='Member', alpha=0.6, s=20)
    plt.scatter(X_tsne[non_member_mask, 0], X_tsne[non_member_mask, 1], 
               c='blue', label='Non-Member', alpha=0.6, s=20)
    
    plt.xlabel('t-SNE Component 1')
    plt.ylabel('t-SNE Component 2')
    plt.title('t-SNE Visualization of MIA Features')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"t-SNE visualization saved to {save_path}")
    
    plt.show()


def plot_pca_visualization(X_test, y_test, save_path=None):
    """
    Create PCA visualization of MIA attack features
    
    Args:
        X_test: Test features
        y_test: Test labels
        save_path: Path to save the plot (optional)
    """
    # Apply PCA
    pca = PCA(n_components=2)
    X_pca = pca.fit_transform(X_test)
    
    # Create plot
    plt.figure(figsize=(10, 8))
    
    member_mask = y_test == 1
    non_member_mask = y_test == 0
    
    plt.scatter(X_pca[member_mask, 0], X_pca[member_mask, 1], 
               c='red', label='Member', alpha=0.6, s=20)
    plt.scatter(X_pca[non_member_mask, 0], X_pca[non_member_mask, 1], 
               c='blue', label='Non-Member', alpha=0.6, s=20)
    
    plt.xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.2%} variance)')
    plt.ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.2%} variance)')
    plt.title('PCA Visualization of MIA Features')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"PCA visualization saved to {save_path}")
    
    plt.show()


def create_comprehensive_mia_report(X_test, y_test, attack_results, save_dir=None):
    """
    Create a comprehensive visualization report for MIA results
    
    Args:
        X_test: Test features
        y_test: Test labels
        attack_results: Dictionary containing attack results
        save_dir: Directory to save plots (optional)
    """
    if save_dir:
        import os
        os.makedirs(save_dir, exist_ok=True)
    
    # 3D scatter plot
    save_path_3d = os.path.join(save_dir, 'mia_3d_scatter.png') if save_dir else None
    plot_mia_attack_results_3d(X_test, y_test, save_path_3d)
    
    # 2D scatter plots
    save_path_2d = os.path.join(save_dir, 'mia_2d_scatter.png') if save_dir else None
    plot_mia_attack_results_2d(X_test, y_test, save_path_2d)
    
    # Probability distributions
    save_path_dist = os.path.join(save_dir, 'probability_distributions.png') if save_dir else None
    plot_probability_distributions(X_test, y_test, save_path_dist)
    
    # t-SNE visualization
    save_path_tsne = os.path.join(save_dir, 'tsne_visualization.png') if save_dir else None
    plot_tsne_visualization(X_test, y_test, save_path_tsne)
    
    # PCA visualization
    save_path_pca = os.path.join(save_dir, 'pca_visualization.png') if save_dir else None
    plot_pca_visualization(X_test, y_test, save_path_pca)
    
    print("Comprehensive MIA visualization report created!")
