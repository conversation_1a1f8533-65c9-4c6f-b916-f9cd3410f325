# CIFAR-100 Membership Inference Attack with MoCo

This project implements a comprehensive framework for studying Membership Inference Attacks (MIA) on self-supervised learning models, specifically using MoCo (Momentum Contrast) on the CIFAR-100 dataset.

## 🎯 Overview

Membership Inference Attacks aim to determine whether a specific data sample was used during the training of a machine learning model. This project provides:

- **MoCo Implementation**: A complete implementation of Momentum Contrast for CIFAR-100
- **MIA Framework**: Tools to perform membership inference attacks using XGBoost
- **Comprehensive Evaluation**: Detailed metrics and visualizations for attack assessment
- **Modular Design**: Clean, extensible codebase with configuration management

## 📁 Project Structure

```
EncoderMU/
├── src/                          # Source code
│   ├── models/                   # Model implementations
│   │   └── moco.py              # MoCo model and components
│   ├── data/                    # Data handling
│   │   └── dataset.py           # Dataset and data loaders
│   ├── attacks/                 # Attack implementations
│   │   └── mia.py              # Membership inference attack
│   └── utils/                   # Utility functions
│       ├── config.py           # Configuration management
│       ├── evaluation.py       # Model evaluation utilities
│       ├── metrics.py          # Metrics calculation
│       └── visualization.py    # Plotting and visualization
├── scripts/                     # Training and evaluation scripts
│   ├── train_moco.py           # MoCo model training
│   └── run_mia_attack.py       # MIA attack execution
├── config/                      # Configuration files
│   └── config.yaml             # Main configuration
├── data/                        # Dataset storage
├── logs/                        # Experiment logs
├── save_model/                  # Saved models
├── data_indices/               # Data split indices
├── main.py                     # Main entry point
├── requirements.txt            # Python dependencies
└── README.md                   # This file
```

## 🚀 Quick Start

### 1. Installation

```bash
# Clone the repository
git clone <repository-url>
cd EncoderMU

# Install dependencies
pip install -r requirements.txt
```

### 2. Prepare Data Indices

Ensure you have the data indices files in the `data_indices/` directory:
- `shadow_data_indices_cifar100.npz`
- `target_data_indices_cifar100.npz`

### 3. Run Experiments

#### Option A: Complete Pipeline
```bash
# Run the entire experiment pipeline
python main.py pipeline
```

#### Option B: Step-by-Step

```bash
# 1. Train shadow model
python main.py train --model_type shadow

# 2. Train target model  
python main.py train --model_type target

# 3. Run MIA attack
python main.py attack --shadow_model ./save_model/Shadow_cifar100_Adam_overfitting_1600epoch.pth --target_model ./save_model/Target_cifar100_Adam_overfitting_1600epoch.pth
```

#### Option C: Individual Scripts

```bash
# Train models
python scripts/train_moco.py --model_type shadow
python scripts/train_moco.py --model_type target

# Run attack
python scripts/run_mia_attack.py --shadow_model ./save_model/shadow_model.pth
```

## ⚙️ Configuration

The project uses YAML configuration files for easy parameter management. Key configurations include:

### Model Parameters
- **Architecture**: ResNet18/34/50 backbone
- **Feature Dimension**: 128
- **Queue Size**: 4096
- **Temperature**: 0.1

### Training Parameters
- **Epochs**: 1600
- **Batch Size**: 512
- **Learning Rate**: 0.01
- **Optimizer**: Adam/SGD

### MIA Parameters
- **Attack Model**: XGBoost
- **Top-K Probabilities**: 3
- **Test Split**: 0.2

Edit `config/config.yaml` to customize these parameters.

## 📊 Results and Evaluation

The framework provides comprehensive evaluation metrics:

### Attack Metrics
- **Accuracy**: Overall attack success rate
- **AUC-ROC**: Area under ROC curve
- **Precision/Recall**: Attack precision and recall
- **Attack Advantage**: TPR - FPR

### Visualizations
- **3D Scatter Plots**: Member vs non-member probability distributions
- **ROC Curves**: Attack performance visualization
- **t-SNE/PCA**: Feature space visualization
- **Confusion Matrices**: Attack result breakdown

### Privacy Risk Assessment
The framework automatically assesses privacy risk levels:
- **CRITICAL**: Attack accuracy ≥ 90%
- **HIGH**: Attack accuracy ≥ 80%
- **MEDIUM**: Attack accuracy ≥ 70%
- **LOW**: Attack accuracy ≥ 60%
- **MINIMAL**: Attack accuracy < 60%

## 🔧 Advanced Usage

### Custom Configuration
```bash
# Use custom configuration file
python main.py train --model_type target --config custom_config.yaml
```

### Resume Training
```bash
# Resume from checkpoint
python main.py train --model_type target --resume ./save_model/checkpoint_epoch_500.pth
```

### Evaluation Only
```bash
# Evaluate existing model
python main.py evaluate --model_path ./save_model/target_model.pth --model_type target
```

## 📈 Understanding the Results

### MIA Attack Success Factors
1. **Model Overfitting**: Higher overfitting leads to better attack success
2. **Training Data Size**: Smaller datasets are more vulnerable
3. **Model Complexity**: More complex models may be more vulnerable
4. **Data Distribution**: Imbalanced datasets affect attack performance

### Interpreting Metrics
- **High AUC-ROC (>0.8)**: Strong attack capability
- **Attack Advantage > 0.3**: Significant privacy risk
- **Balanced Precision/Recall**: Robust attack performance

## 🛡️ Privacy Protection Recommendations

Based on attack results, the framework provides recommendations:

### For High-Risk Models
- Implement differential privacy
- Use regularization techniques
- Apply data augmentation
- Consider ensemble methods

### For Medium-Risk Models
- Monitor for overfitting
- Implement privacy-preserving techniques
- Use cross-validation

## 🔬 Research Applications

This framework can be used for:

- **Privacy Research**: Studying membership inference vulnerabilities
- **Model Security**: Evaluating model privacy risks
- **Defense Development**: Testing privacy-preserving techniques
- **Comparative Studies**: Comparing different model architectures

## 📚 References

1. **MoCo**: He, K., et al. "Momentum contrast for unsupervised visual representation learning." CVPR 2020.
2. **Membership Inference**: Shokri, R., et al. "Membership inference attacks against machine learning models." S&P 2017.
3. **CIFAR-100**: Krizhevsky, A. "Learning multiple layers of features from tiny images." 2009.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit issues, feature requests, or pull requests.

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Original MoCo implementation by Facebook Research
- CIFAR-100 dataset by Alex Krizhevsky
- XGBoost library for attack model implementation
