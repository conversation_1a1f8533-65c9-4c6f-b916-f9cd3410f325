"""
Membership Inference Attack (MIA) implementation
"""

import torch
import torch.nn.functional as F
import numpy as np
import pickle
import matplotlib.pyplot as plt
from sklearn.metrics import classification_report, accuracy_score, roc_auc_score
from sklearn.model_selection import train_test_split
from xgboost import XGBClassifier
from mpl_toolkits.mplot3d import Axes3D


def MIA_data(shadow_model, in_data, out_data, device):
    """
    Generate MIA attack data from shadow model predictions
    
    Args:
        shadow_model: The shadow model to attack
        in_data: Data that was used to train the shadow model (members)
        out_data: Data that was not used to train the shadow model (non-members)
        device: Device to run computations on
        
    Returns:
        data: Feature vectors for attack model training
        target: Labels (1 for members, 0 for non-members)
    """
    # Move data to device
    in_data = in_data.to(device)
    out_data = out_data.to(device)

    # Set model to evaluation mode
    shadow_model.eval()

    attack_x = []
    attack_y = []

    # Process member data (in_data)
    with torch.no_grad():
        pred = F.softmax(shadow_model(in_data), dim=1)
        pred, _ = torch.sort(pred, descending=True)
        pred = pred[:, :3]  # Take top 3 probabilities
        
        # Calculate average prediction for logging
        pred_in = pred[0]
        for i in range(len(pred)):
            if i > 1:
                pred_in = pred_in + pred[i]
        print(f"Average member prediction: {pred_in/len(pred)}")
        
        attack_x.append(pred.detach())
        attack_y.append(torch.ones(size=(in_data.shape[0],)).to(device))

    # Process non-member data (out_data)
    with torch.no_grad():
        pred = F.softmax(shadow_model(out_data), dim=1)
        pred, _ = torch.sort(pred, descending=True)
        pred = pred[:, :3]  # Take top 3 probabilities
        
        # Calculate average prediction for logging
        pred_out = pred[0]
        for i in range(len(pred)):
            if i > 1:
                pred_out = pred_out + pred[i]
        print(f"Average non-member prediction: {pred_out/len(pred)}")

        attack_x.append(pred.detach())
        attack_y.append(torch.zeros(size=(out_data.shape[0],)).to(device))

    # Concatenate all data
    tensor_x = torch.cat(attack_x)
    tensor_y = torch.cat(attack_y)

    # Move data back to CPU
    data = tensor_x.detach().cpu().numpy()
    target = tensor_y.detach().cpu().numpy()

    return data, target


def train_attacker(data_x, data_y, save_path='./save_model/MIA_attackModel.pkl', test_size=0.2):
    """
    Train the MIA attack model using XGBoost
    
    Args:
        data_x: Feature vectors
        data_y: Labels (1 for members, 0 for non-members)
        save_path: Path to save the trained attack model
        test_size: Fraction of data to use for testing
        
    Returns:
        train_acc: Training accuracy
        test_acc: Testing accuracy
    """
    # Split data into train and test sets
    X_train, X_test, y_train, y_test = train_test_split(data_x, data_y, test_size=test_size, random_state=42)

    # Train XGBoost classifier
    attack_model = XGBClassifier(n_jobs=4, objective='binary:logistic', booster="gbtree", random_state=42)
    attack_model.fit(X_train, y_train)
    
    # Save the trained model
    pickle.dump(attack_model, open(save_path, 'wb'))

    # Make predictions
    y_train_pred = attack_model.predict(X_train)
    y_test_pred = attack_model.predict(X_test)

    # Calculate prediction probabilities for AUC
    y_train_pred_prob = attack_model.predict_proba(X_train)[:, 1]
    y_test_pred_prob = attack_model.predict_proba(X_test)[:, 1]

    # Calculate metrics
    train_acc = accuracy_score(y_train, y_train_pred)
    test_acc = accuracy_score(y_test, y_test_pred)
    train_auc = roc_auc_score(y_train, y_train_pred_prob)
    test_auc = roc_auc_score(y_test, y_test_pred_prob)

    # Print results
    print(f"MIA Attacker training accuracy: {train_acc}")
    print(f"MIA Attacker testing accuracy: {test_acc}")
    print(f"Training AUC: {train_auc}")
    print(f"Testing AUC: {test_auc}")

    print("Training classification report:")
    print(classification_report(y_train, y_train_pred))

    print("Testing classification report:")
    print(classification_report(y_test, y_test_pred))

    # Create 3D scatter plot
    plot_mia_results(X_test, y_test)

    return train_acc, test_acc


def plot_mia_results(X_test, y_test):
    """
    Create a 3D scatter plot of MIA results
    
    Args:
        X_test: Test features (should have at least 3 dimensions)
        y_test: Test labels
    """
    # Separate member and non-member data
    member_data = X_test[y_test == 1]
    non_member_data = X_test[y_test == 0]

    # Create 3D scatter plot
    fig = plt.figure(figsize=(10, 8))
    ax = fig.add_subplot(111, projection='3d')

    # Plot using first three probabilities
    ax.scatter(member_data[:, 0], member_data[:, 1], member_data[:, 2], 
               c='red', label='Member', alpha=0.6)
    ax.scatter(non_member_data[:, 0], non_member_data[:, 1], non_member_data[:, 2], 
               c='blue', label='Non-Member', alpha=0.6)

    ax.set_xlabel('First Probability')
    ax.set_ylabel('Second Probability')
    ax.set_zlabel('Third Probability')
    ax.legend()
    ax.set_title('Member vs Non-Member Data Scatter Plot (3D)')

    plt.tight_layout()
    plt.show()


def load_attack_model(model_path='./save_model/MIA_attackModel.pkl'):
    """
    Load a trained MIA attack model
    
    Args:
        model_path: Path to the saved attack model
        
    Returns:
        attack_model: Loaded XGBoost model
    """
    with open(model_path, 'rb') as f:
        attack_model = pickle.load(f)
    return attack_model


def evaluate_mia_attack(attack_model, test_data, test_labels):
    """
    Evaluate the MIA attack model on test data
    
    Args:
        attack_model: Trained attack model
        test_data: Test features
        test_labels: True labels
        
    Returns:
        accuracy: Attack accuracy
        auc: AUC score
    """
    predictions = attack_model.predict(test_data)
    pred_probs = attack_model.predict_proba(test_data)[:, 1]
    
    accuracy = accuracy_score(test_labels, predictions)
    auc = roc_auc_score(test_labels, pred_probs)
    
    print(f"Attack Accuracy: {accuracy:.4f}")
    print(f"Attack AUC: {auc:.4f}")
    print("\nClassification Report:")
    print(classification_report(test_labels, predictions))
    
    return accuracy, auc
