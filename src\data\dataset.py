"""
Dataset and data loading utilities for CIFAR-100 MIA experiments
"""

import numpy as np
import torch
from torch.utils.data import DataLoader, Subset
from torchvision import transforms, datasets
from torchvision.datasets import CIFAR100
from PIL import Image


class CIFAR100Pair(CIFAR100):
    """CIFAR100 Dataset that returns pairs of augmented images for contrastive learning"""
    
    def __getitem__(self, index):
        img = self.data[index]
        targets = self.targets[index]
        img = Image.fromarray(img)
        
        if self.transform is not None:
            im_1 = self.transform(img)
            im_2 = self.transform(img)

        return im_1, im_2


def get_transforms():
    """Get data transforms for training and testing"""
    
    train_transform = transforms.Compose([
        transforms.RandomResizedCrop(32, scale=(0.2, 1.)),
        transforms.RandomHorizontalFlip(p=0.5),
        transforms.RandomApply([transforms.ColorJitter(0.4, 0.4, 0.4, 0.1)], p=0.8),
        transforms.RandomGrayscale(p=0.2),
        transforms.ToTensor(),
        transforms.Normalize([0.4914, 0.4822, 0.4465], [0.2023, 0.1994, 0.2010])
    ])

    test_transform = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize([0.4914, 0.4822, 0.4465], [0.2023, 0.1994, 0.2010])
    ])
    
    return train_transform, test_transform


def load_shadow_indices(file_path='./data_indices/shadow_data_indices_cifar100.npz'):
    """Load shadow model data indices"""
    with np.load(file_path) as f:
        indices = [f['arr_%d' % i] for i in range(len(f.files))]
    return indices


def load_target_indices(file_path='./data_indices/target_data_indices_cifar100.npz'):
    """Load target model data indices"""
    with np.load(file_path) as f:
        indices = [f['arr_%d' % i] for i in range(len(f.files))]
    return indices


def create_data_loaders(config):
    """
    Create all necessary data loaders for the experiment
    
    Args:
        config: Configuration object containing data parameters
        
    Returns:
        Dictionary containing all data loaders
    """
    train_transform, test_transform = get_transforms()
    
    # Load datasets
    train_data = CIFAR100Pair(root='data', train=True, transform=train_transform, download=True)
    memory_data = CIFAR100(root='data', train=True, transform=train_transform, download=True)
    test_data = CIFAR100(root='data', train=False, transform=test_transform, download=True)
    
    # Load indices
    indices_shadow = load_shadow_indices()[0]
    indices_target = load_target_indices()[0]
    
    # Create specific index sets
    indices_shadow_train = indices_shadow[:config.shadow_train_size]
    out_train = indices_shadow[10000:12000]
    train_shadow = indices_shadow[:config.shadow_train_size]
    mia_out = indices_target[:config.mia_out_size]
    training_shadowin_data = indices_shadow[:10000]
    training_shadowin_data_target = indices_shadow[10000:20000]
    train_test = indices_shadow[:10000]
    
    # Create remaining indices for test data
    total_train_indices = np.arange(50000)
    remaining_indices = np.setdiff1d(total_train_indices, indices_shadow)
    random_indices = np.random.choice(remaining_indices, config.mia_out_size, replace=False)
    indices_shadow_test = np.random.choice(np.arange(10000), config.mia_out_size, replace=False)
    
    # Create data loaders
    loaders = {}
    
    # Target model training loader
    train_data_target = Subset(dataset=train_data, indices=indices_target)
    loaders['target_train'] = DataLoader(
        train_data_target, 
        batch_size=config.batch_size, 
        shuffle=True, 
        num_workers=config.num_workers, 
        pin_memory=True, 
        drop_last=True
    )
    
    # Shadow model training loaders
    training_data = Subset(dataset=train_data, indices=training_shadowin_data)
    loaders['shadow_train'] = DataLoader(
        training_data, 
        batch_size=config.batch_size, 
        shuffle=True, 
        num_workers=config.num_workers, 
        pin_memory=True, 
        drop_last=True
    )
    
    training_data_target = Subset(dataset=train_data, indices=training_shadowin_data_target)
    loaders['shadow_train_target'] = DataLoader(
        training_data_target, 
        batch_size=config.batch_size, 
        shuffle=True, 
        num_workers=config.num_workers, 
        pin_memory=True, 
        drop_last=True
    )
    
    # Evaluation loaders
    ACC_shadow = Subset(dataset=memory_data, indices=train_test)
    loaders['eval_memory'] = DataLoader(
        ACC_shadow, 
        batch_size=config.batch_size, 
        shuffle=False, 
        num_workers=config.num_workers, 
        pin_memory=True
    )
    
    # Test loader
    loaders['test'] = DataLoader(
        test_data, 
        batch_size=config.batch_size, 
        shuffle=False, 
        num_workers=config.num_workers, 
        pin_memory=True
    )
    
    # MIA data loaders
    train_data_shadow = Subset(dataset=memory_data, indices=training_shadowin_data)
    loaders['mia_train'] = DataLoader(
        train_data_shadow, 
        batch_size=config.batch_size, 
        shuffle=False, 
        num_workers=config.num_workers, 
        pin_memory=True
    )
    
    test_data_sup = Subset(dataset=test_data, indices=indices_shadow_test)
    loaders['mia_test'] = DataLoader(
        test_data_sup, 
        batch_size=config.batch_size, 
        shuffle=False, 
        num_workers=config.num_workers, 
        pin_memory=True, 
        drop_last=True
    )
    
    # Store targets for evaluation
    loaders['memory_targets'] = np.array(memory_data.targets)[train_test]
    
    return loaders


def save_mia_data(train_loader, test_loader, save_dir='logs'):
    """
    Save MIA training and test data to disk
    
    Args:
        train_loader: DataLoader for training data
        test_loader: DataLoader for test data
        save_dir: Directory to save the data
    """
    # Process training data
    train_datasets = []
    train_targets = []
    for i, (data, target) in enumerate(train_loader):
        train_datasets.append(data.cpu().numpy())
        train_targets.append(target.cpu().numpy())
    train_datasets = np.concatenate(train_datasets)
    train_targets = np.concatenate(train_targets)
    
    # Process test data
    test_datasets = []
    test_targets = []
    for i, (data, target) in enumerate(test_loader):
        test_datasets.append(data.cpu().numpy())
        test_targets.append(target.cpu().numpy())
    test_datasets = np.concatenate(test_datasets)
    test_targets = np.concatenate(test_targets)
    
    # Save data
    np.savez(f'{save_dir}/cifar100train.npz', x=train_datasets, y=train_targets)
    np.savez(f'{save_dir}/cifar100test.npz', x=test_datasets, y=test_targets)
    
    print(f"Data saved to {save_dir}/")


def load_mia_data(save_dir='logs'):
    """
    Load MIA data from disk
    
    Args:
        save_dir: Directory containing the saved data
        
    Returns:
        shadowin: Tuple of (train_data, train_labels)
        shadowout: Tuple of (test_data, test_labels)
    """
    # Load training data (shadow in)
    train_data = torch.from_numpy(np.load(f'{save_dir}/cifar100train.npz')['x'])
    train_label = torch.from_numpy(np.load(f'{save_dir}/cifar100train.npz')['y'])
    shadowin = train_data, train_label
    
    # Load test data (shadow out)
    test_data = torch.from_numpy(np.load(f'{save_dir}/cifar100test.npz')['x'])
    test_label = torch.from_numpy(np.load(f'{save_dir}/cifar100test.npz')['y'])
    shadowout = test_data, test_label
    
    return shadowin, shadowout
