"""
Evaluation utilities for model testing and KNN classification
"""

import torch
import torch.nn.functional as F
import numpy as np
from tqdm import tqdm


def test(net, memory_data_loader, test_data_loader, memory_data_targets, device, classes=100, knn_k=200, knn_t=0.1):
    """
    Test the model using KNN classification
    
    Args:
        net: The model to test
        memory_data_loader: DataLoader for memory bank data
        test_data_loader: DataLoader for test data
        memory_data_targets: Target labels for memory data
        device: Device to run computations on
        classes: Number of classes
        knn_k: Number of nearest neighbors
        knn_t: Temperature for KNN
        
    Returns:
        accuracy: Top-1 accuracy percentage
    """
    net.eval()
    total_top1, total_num, feature_bank = 0.0, 0, []
    
    with torch.no_grad():
        # Generate feature bank
        for data, target in tqdm(memory_data_loader, desc='Feature extracting'):
            feature = net(data.to(device))
            feature = F.normalize(feature, dim=1)
            feature_bank.append(feature)
        
        # [D, N]
        feature_bank = torch.cat(feature_bank, dim=0).t().contiguous()
        # [N]
        feature_labels = torch.tensor(memory_data_targets, device=feature_bank.device)
        
        # Loop test data to predict the label by weighted knn search
        test_bar = tqdm(test_data_loader)
        for data, target in test_bar:
            data, target = data.to(device), target.to(device)
            feature = net(data)
            feature = F.normalize(feature, dim=1)
            
            pred_labels = knn_predict(feature, feature_bank, feature_labels, classes, knn_k, knn_t)

            total_num += data.size(0)
            total_top1 += (pred_labels[:, 0] == target).float().sum().item()
            test_bar.set_description('Acc@1:{:.2f}%'.format(total_top1 / total_num * 100))
    
    return total_top1 / total_num * 100


def knn_predict(feature, feature_bank, feature_labels, classes, knn_k, knn_t):
    """
    KNN prediction using cosine similarity
    
    Args:
        feature: Query features
        feature_bank: Memory bank features
        feature_labels: Labels for memory bank
        classes: Number of classes
        knn_k: Number of nearest neighbors
        knn_t: Temperature parameter
        
    Returns:
        pred_labels: Predicted labels sorted by confidence
    """
    # Compute cos similarity between each feature vector and feature bank ---> [B, N]
    sim_matrix = torch.mm(feature, feature_bank)
    # [B, K]
    sim_weight, sim_indices = sim_matrix.topk(k=knn_k, dim=-1)
    # [B, K]
    sim_labels = torch.gather(feature_labels.expand(feature.size(0), -1), dim=-1, index=sim_indices)
    sim_weight = (sim_weight / knn_t).exp()

    # Counts for each class
    one_hot_label = torch.zeros(feature.size(0) * knn_k, classes, device=sim_labels.device)
    # [B*K, C]
    one_hot_label = one_hot_label.scatter(dim=-1, index=sim_labels.view(-1, 1), value=1.0)
    # Weighted score ---> [B, C]
    pred_scores = torch.sum(one_hot_label.view(feature.size(0), -1, classes) * sim_weight.unsqueeze(dim=-1), dim=1)

    pred_labels = pred_scores.argsort(dim=-1, descending=True)
    return pred_labels


def compute_gradient_penalty(D, real_samples, fake_samples, device):
    """
    Calculates the gradient penalty loss for WGAN GP
    
    Args:
        D: Discriminator model
        real_samples: Real data samples
        fake_samples: Generated/fake data samples
        device: Device to run computations on
        
    Returns:
        gradient_penalty: Computed gradient penalty
    """
    # Random weight term for interpolation between real and fake samples
    alpha = torch.cuda.FloatTensor(np.random.random((real_samples.size(0), 1, 1, 1))).to(device)
    
    # Get random interpolation between real and fake samples
    interpolates = (alpha * real_samples + ((1 - alpha) * fake_samples)).requires_grad_(True)
    d_interpolates = D(interpolates)
    
    fake = torch.cuda.FloatTensor(real_samples.shape[0], d_interpolates.shape[1]).fill_(1.0).to(device)
    fake.requires_grad_(False)

    # Get gradient w.r.t. interpolates
    gradients = torch.autograd.grad(
        outputs=d_interpolates,
        inputs=interpolates,
        grad_outputs=fake,
        create_graph=True,
        retain_graph=True,
        only_inputs=True,
    )[0]
    
    gradients = gradients.view(gradients.size(0), -1)
    gradient_penalty = ((gradients.norm(2, dim=1) - 1) ** 2).mean()
    
    return gradient_penalty


def compute_gradient_penalty_real(D, real_samples, device):
    """
    Calculates the gradient penalty loss for real samples only
    
    Args:
        D: Discriminator model
        real_samples: Real data samples
        device: Device to run computations on
        
    Returns:
        gradient_penalty: Computed gradient penalty
    """
    real_samples.requires_grad_(True)
    d_real_samples = D(real_samples)

    fake = torch.cuda.FloatTensor(real_samples.shape[0], d_real_samples.shape[1]).fill_(1.0).to(device)
    fake.requires_grad_(False)

    # Get gradient w.r.t. real_samples
    gradients = torch.autograd.grad(
        outputs=d_real_samples,
        inputs=real_samples,
        grad_outputs=fake,
        create_graph=True,
        retain_graph=True,
        only_inputs=True,
    )[0]
    
    gradients = gradients.view(gradients.size(0), -1)
    gradient_penalty = ((gradients.norm(2, dim=1)) - 1 ** 2).mean()

    return gradient_penalty
