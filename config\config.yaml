# Configuration file for CIFAR-100 MIA experiments

# Model configuration
model:
  # MoCo model parameters
  moco:
    dim: 128                    # Feature dimension
    K: 4096                     # Queue size
    m: 0.99                     # Momentum coefficient
    T: 0.1                      # Temperature parameter
    arch: "resnet18"            # Architecture (resnet18, resnet34, resnet50)
    bn_splits: 8                # BatchNorm splits
    symmetric: true             # Use symmetric loss

# Training configuration
training:
  # General training parameters
  num_epochs: 1600              # Number of training epochs
  batch_size: 512               # Batch size
  learning_rate: 0.01           # Learning rate
  optimizer: "adam"             # Optimizer type (adam, sgd)
  momentum: 0.9                 # SGD momentum (if using SGD)
  weight_decay: 5e-4            # Weight decay
  
  # Learning rate scheduling
  lr_scheduler:
    type: "cosine"              # Scheduler type (cosine, step, exponential)
    warmup_epochs: 10           # Warmup epochs
    
  # Evaluation frequency
  eval_frequency: 100           # Evaluate every N epochs
  save_frequency: 500           # Save model every N epochs

# Data configuration
data:
  # Dataset parameters
  dataset: "cifar100"           # Dataset name
  data_root: "data"             # Data root directory
  num_workers: 16               # Number of data loading workers
  pin_memory: true              # Pin memory for faster GPU transfer
  
  # Data splits
  shadow_train_size: 2000       # Size of shadow training set
  mia_out_size: 2000           # Size of MIA out-of-training set
  test_split: 0.2              # Test split ratio for MIA attack model
  
  # Data indices files
  shadow_indices_file: "./data_indices/shadow_data_indices_cifar100.npz"
  target_indices_file: "./data_indices/target_data_indices_cifar100.npz"

# MIA Attack configuration
mia:
  # Attack model parameters
  attack_model: "xgboost"       # Attack model type
  n_jobs: 4                     # Number of parallel jobs for XGBoost
  objective: "binary:logistic"  # XGBoost objective
  booster: "gbtree"            # XGBoost booster type
  
  # Feature extraction
  top_k_probs: 3               # Number of top probabilities to use as features
  
  # Evaluation
  plot_results: true           # Whether to plot 3D scatter plot
  save_attack_model: true      # Whether to save trained attack model

# Evaluation configuration
evaluation:
  # KNN parameters for model evaluation
  knn_k: 200                   # Number of nearest neighbors
  knn_t: 0.1                   # Temperature for KNN
  classes: 100                 # Number of classes in CIFAR-100

# Paths configuration
paths:
  # Model saving paths
  save_model_dir: "./save_model"
  logs_dir: "./logs"
  
  # Specific model files
  target_model_file: "Target_cifar100_Adam_overfitting_1600epoch.pth"
  shadow_model_file: "Shadow_cifar100_Adam_overfitting_1600epoch.pth"
  attack_model_file: "MIA_attackModel.pkl"

# Hardware configuration
hardware:
  # Device configuration
  device: "auto"               # Device to use (auto, cuda, cpu)
  gpu_id: 0                    # GPU ID to use (if multiple GPUs available)
  
  # Memory optimization
  mixed_precision: false       # Use mixed precision training
  gradient_checkpointing: false # Use gradient checkpointing to save memory

# Logging configuration
logging:
  # Logging level and format
  level: "INFO"                # Logging level (DEBUG, INFO, WARNING, ERROR)
  log_file: "./logs/experiment.log"  # Log file path
  
  # Experiment tracking
  use_wandb: false             # Use Weights & Biases for experiment tracking
  wandb_project: "cifar100-mia"  # W&B project name
  experiment_name: "mia_experiment"  # Experiment name
  
  # Progress tracking
  progress_bar: true           # Show progress bars
  print_frequency: 10          # Print training stats every N batches

# Reproducibility
reproducibility:
  seed: 42                     # Random seed for reproducibility
  deterministic: true          # Use deterministic algorithms (may be slower)
  benchmark: false             # Use cudnn benchmark (faster but less reproducible)
