"""
Example usage script for CIFAR-100 MIA experiments
This script demonstrates how to use the framework step by step
"""

import os
import sys
import logging

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from utils.config import load_config, setup_reproducibility, setup_logging


def example_quick_start():
    """Example of quick start usage"""
    print("=" * 60)
    print("CIFAR-100 MIA Framework - Quick Start Example")
    print("=" * 60)
    
    # Load configuration
    print("1. Loading configuration...")
    config = load_config()
    
    # Setup reproducibility
    print("2. Setting up reproducibility...")
    setup_reproducibility(config)
    
    # Setup logging
    print("3. Setting up logging...")
    logger = setup_logging(config)
    
    print("4. Configuration loaded successfully!")
    print(f"   - Device: {config.device}")
    print(f"   - Model Architecture: {config.model.moco.arch}")
    print(f"   - Training Epochs: {config.training.num_epochs}")
    print(f"   - Batch Size: {config.training.batch_size}")
    
    print("\n" + "=" * 60)
    print("Next Steps:")
    print("=" * 60)
    print("To run the complete experiment pipeline:")
    print("  python main.py pipeline")
    print()
    print("To train models individually:")
    print("  python main.py train --model_type shadow")
    print("  python main.py train --model_type target")
    print()
    print("To run MIA attack:")
    print("  python main.py attack --shadow_model ./save_model/shadow_model.pth")
    print()
    print("For more options:")
    print("  python main.py --help")
    print("=" * 60)


def example_custom_config():
    """Example of using custom configuration"""
    print("\n" + "=" * 60)
    print("Custom Configuration Example")
    print("=" * 60)
    
    # Load default config
    config = load_config()
    
    # Modify some parameters
    print("Modifying configuration parameters...")
    config.update('training.num_epochs', 100)  # Reduce epochs for testing
    config.update('training.batch_size', 256)  # Reduce batch size
    config.update('model.moco.arch', 'resnet34')  # Change architecture
    
    print(f"Updated epochs: {config.training.num_epochs}")
    print(f"Updated batch size: {config.training.batch_size}")
    print(f"Updated architecture: {config.model.moco.arch}")
    
    # Save modified config
    config.save('config/custom_config.yaml')
    print("Custom configuration saved to config/custom_config.yaml")
    
    print("\nTo use custom config:")
    print("  python main.py train --model_type target --config config/custom_config.yaml")


def example_data_preparation():
    """Example of data preparation"""
    print("\n" + "=" * 60)
    print("Data Preparation Example")
    print("=" * 60)
    
    # Check if data indices exist
    shadow_indices_path = "./data_indices/shadow_data_indices_cifar100.npz"
    target_indices_path = "./data_indices/target_data_indices_cifar100.npz"
    
    if os.path.exists(shadow_indices_path) and os.path.exists(target_indices_path):
        print("✓ Data indices files found:")
        print(f"  - {shadow_indices_path}")
        print(f"  - {target_indices_path}")
    else:
        print("✗ Data indices files not found!")
        print("Please ensure you have the following files:")
        print(f"  - {shadow_indices_path}")
        print(f"  - {target_indices_path}")
        print("\nThese files should contain the data split indices for shadow and target models.")
        
        # Create example indices (for demonstration)
        print("\nCreating example data indices...")
        import numpy as np
        
        # Create directories if they don't exist
        os.makedirs("data_indices", exist_ok=True)
        
        # Generate random indices for demonstration
        np.random.seed(42)
        total_samples = 50000  # CIFAR-100 training samples
        
        # Shadow model indices (first 25000 samples)
        shadow_indices = np.arange(25000)
        np.savez(shadow_indices_path, shadow_indices)
        
        # Target model indices (last 25000 samples)
        target_indices = np.arange(25000, 50000)
        np.savez(target_indices_path, target_indices)
        
        print("✓ Example data indices created!")
        print("Note: These are random indices for demonstration. In practice,")
        print("you should use carefully designed data splits for your experiments.")


def example_model_evaluation():
    """Example of model evaluation"""
    print("\n" + "=" * 60)
    print("Model Evaluation Example")
    print("=" * 60)
    
    # Check for existing models
    save_model_dir = "./save_model"
    target_model = os.path.join(save_model_dir, "Target_cifar100_Adam_overfitting_1600epoch.pth")
    shadow_model = os.path.join(save_model_dir, "Shadow_cifar100_Adam_overfitting_1600epoch.pth")
    
    if os.path.exists(target_model):
        print(f"✓ Target model found: {target_model}")
        print("To evaluate:")
        print(f"  python main.py evaluate --model_path {target_model} --model_type target")
    else:
        print(f"✗ Target model not found: {target_model}")
        print("Train the target model first:")
        print("  python main.py train --model_type target")
    
    if os.path.exists(shadow_model):
        print(f"✓ Shadow model found: {shadow_model}")
        print("To evaluate:")
        print(f"  python main.py evaluate --model_path {shadow_model} --model_type shadow")
    else:
        print(f"✗ Shadow model not found: {shadow_model}")
        print("Train the shadow model first:")
        print("  python main.py train --model_type shadow")


def main():
    """Main example function"""
    try:
        # Run examples
        example_quick_start()
        example_custom_config()
        example_data_preparation()
        example_model_evaluation()
        
        print("\n" + "=" * 60)
        print("Examples completed successfully!")
        print("You can now start using the framework.")
        print("=" * 60)
        
    except Exception as e:
        print(f"Error running examples: {str(e)}")
        print("Please check your installation and try again.")


if __name__ == '__main__':
    main()
