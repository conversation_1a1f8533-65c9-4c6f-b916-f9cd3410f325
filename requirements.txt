# Core ML and Deep Learning
torch>=1.9.0
torchvision>=0.10.0
numpy>=1.21.0

# Data Science and ML
scikit-learn>=1.0.0
pandas>=1.3.0
xgboost>=1.5.0

# Visualization
matplotlib>=3.4.0
seaborn>=0.11.0

# Configuration and Utilities
PyYAML>=5.4.0
tqdm>=4.62.0

# Image Processing
Pillow>=8.3.0

# Optional: Advanced Visualization and Analysis
# plotly>=5.0.0  # For interactive plots
# wandb>=0.12.0  # For experiment tracking

# Development and Testing (optional)
# pytest>=6.2.0
# black>=21.0.0
# flake8>=3.9.0

# System and File Handling
# pathlib2>=2.3.0  # For Python < 3.4 compatibility (usually not needed)

# Memory and Performance (optional)
# psutil>=5.8.0  # For system monitoring
# memory-profiler>=0.60.0  # For memory profiling
