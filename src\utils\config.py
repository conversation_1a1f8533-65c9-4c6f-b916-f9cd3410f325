"""
Configuration management utilities
"""

import yaml
import os
import torch
from types import SimpleNamespace


class Config:
    """Configuration class that loads and manages experiment settings"""
    
    def __init__(self, config_path=None):
        """
        Initialize configuration
        
        Args:
            config_path: Path to the configuration YAML file
        """
        if config_path is None:
            config_path = os.path.join(os.path.dirname(__file__), '../../config/config.yaml')
        
        self.config_path = config_path
        self._load_config()
        self._setup_device()
        self._create_directories()
    
    def _load_config(self):
        """Load configuration from YAML file"""
        with open(self.config_path, 'r', encoding='utf-8') as f:
            config_dict = yaml.safe_load(f)
        
        # Convert nested dictionaries to SimpleNamespace for easier access
        self._config = self._dict_to_namespace(config_dict)
    
    def _dict_to_namespace(self, d):
        """Recursively convert dictionary to SimpleNamespace"""
        if isinstance(d, dict):
            return SimpleNamespace(**{k: self._dict_to_namespace(v) for k, v in d.items()})
        elif isinstance(d, list):
            return [self._dict_to_namespace(item) for item in d]
        else:
            return d
    
    def _setup_device(self):
        """Setup device configuration"""
        if self._config.hardware.device == "auto":
            if torch.cuda.is_available():
                self.device = torch.device(f"cuda:{self._config.hardware.gpu_id}")
            else:
                self.device = torch.device("cpu")
        else:
            self.device = torch.device(self._config.hardware.device)
        
        print(f"Using device: {self.device}")
    
    def _create_directories(self):
        """Create necessary directories"""
        directories = [
            self._config.paths.save_model_dir,
            self._config.paths.logs_dir,
            self._config.data.data_root,
            os.path.dirname(self._config.logging.log_file)
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    def __getattr__(self, name):
        """Allow direct access to configuration attributes"""
        return getattr(self._config, name)
    
    def get(self, key, default=None):
        """Get configuration value with default"""
        try:
            keys = key.split('.')
            value = self._config
            for k in keys:
                value = getattr(value, k)
            return value
        except AttributeError:
            return default
    
    def update(self, key, value):
        """Update configuration value"""
        keys = key.split('.')
        config = self._config
        for k in keys[:-1]:
            config = getattr(config, k)
        setattr(config, keys[-1], value)
    
    def save(self, path=None):
        """Save current configuration to file"""
        if path is None:
            path = self.config_path
        
        config_dict = self._namespace_to_dict(self._config)
        with open(path, 'w', encoding='utf-8') as f:
            yaml.dump(config_dict, f, default_flow_style=False, indent=2)
    
    def _namespace_to_dict(self, ns):
        """Convert SimpleNamespace back to dictionary"""
        if isinstance(ns, SimpleNamespace):
            return {k: self._namespace_to_dict(v) for k, v in ns.__dict__.items()}
        elif isinstance(ns, list):
            return [self._namespace_to_dict(item) for item in ns]
        else:
            return ns
    
    def print_config(self):
        """Print current configuration"""
        print("=" * 50)
        print("Configuration:")
        print("=" * 50)
        self._print_namespace(self._config)
        print("=" * 50)
    
    def _print_namespace(self, ns, indent=0):
        """Recursively print namespace contents"""
        for key, value in ns.__dict__.items():
            if isinstance(value, SimpleNamespace):
                print("  " * indent + f"{key}:")
                self._print_namespace(value, indent + 1)
            else:
                print("  " * indent + f"{key}: {value}")


def setup_reproducibility(config):
    """Setup reproducibility settings"""
    if hasattr(config.reproducibility, 'seed'):
        import random
        import numpy as np
        
        # Set seeds
        random.seed(config.reproducibility.seed)
        np.random.seed(config.reproducibility.seed)
        torch.manual_seed(config.reproducibility.seed)
        
        if torch.cuda.is_available():
            torch.cuda.manual_seed(config.reproducibility.seed)
            torch.cuda.manual_seed_all(config.reproducibility.seed)
        
        # Set deterministic behavior
        if config.reproducibility.deterministic:
            torch.backends.cudnn.deterministic = True
            torch.backends.cudnn.benchmark = False
        elif config.reproducibility.benchmark:
            torch.backends.cudnn.benchmark = True
        
        print(f"Reproducibility setup with seed: {config.reproducibility.seed}")


def setup_logging(config):
    """Setup logging configuration"""
    import logging
    
    # Create logger
    logger = logging.getLogger()
    logger.setLevel(getattr(logging, config.logging.level))
    
    # Clear existing handlers
    logger.handlers.clear()
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(getattr(logging, config.logging.level))
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # File handler
    if config.logging.log_file:
        file_handler = logging.FileHandler(config.logging.log_file)
        file_handler.setLevel(getattr(logging, config.logging.level))
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger


def load_config(config_path=None):
    """
    Load configuration from file
    
    Args:
        config_path: Path to configuration file
        
    Returns:
        Config object
    """
    return Config(config_path)
