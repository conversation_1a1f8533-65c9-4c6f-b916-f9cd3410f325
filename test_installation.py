"""
Installation test script for CIFAR-100 MIA framework
This script verifies that all dependencies are installed correctly
"""

import sys
import importlib
import os


def test_python_version():
    """Test Python version compatibility"""
    print("Testing Python version...")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 7:
        print(f"✓ Python {version.major}.{version.minor}.{version.micro} - OK")
        return True
    else:
        print(f"✗ Python {version.major}.{version.minor}.{version.micro} - Requires Python 3.7+")
        return False


def test_dependencies():
    """Test required dependencies"""
    print("\nTesting dependencies...")
    
    required_packages = [
        'torch',
        'torchvision', 
        'numpy',
        'sklearn',
        'pandas',
        'xgboost',
        'matplotlib',
        'seaborn',
        'yaml',
        'tqdm',
        'PIL'
    ]
    
    failed_imports = []
    
    for package in required_packages:
        try:
            if package == 'sklearn':
                importlib.import_module('sklearn')
            elif package == 'yaml':
                importlib.import_module('yaml')
            elif package == 'PIL':
                importlib.import_module('PIL')
            else:
                importlib.import_module(package)
            print(f"✓ {package} - OK")
        except ImportError as e:
            print(f"✗ {package} - FAILED: {str(e)}")
            failed_imports.append(package)
    
    return len(failed_imports) == 0, failed_imports


def test_torch_cuda():
    """Test PyTorch CUDA availability"""
    print("\nTesting PyTorch CUDA...")
    try:
        import torch
        if torch.cuda.is_available():
            print(f"✓ CUDA available - {torch.cuda.device_count()} GPU(s)")
            print(f"  Current device: {torch.cuda.get_device_name()}")
        else:
            print("⚠ CUDA not available - will use CPU")
        return True
    except Exception as e:
        print(f"✗ PyTorch CUDA test failed: {str(e)}")
        return False


def test_project_structure():
    """Test project structure"""
    print("\nTesting project structure...")
    
    required_dirs = [
        'src',
        'src/models',
        'src/data', 
        'src/attacks',
        'src/utils',
        'scripts',
        'config',
        'logs',
        'save_model',
        'data_indices'
    ]
    
    required_files = [
        'main.py',
        'requirements.txt',
        'README.md',
        'config/config.yaml',
        'src/models/moco.py',
        'src/data/dataset.py',
        'src/attacks/mia.py',
        'src/utils/config.py',
        'scripts/train_moco.py',
        'scripts/run_mia_attack.py'
    ]
    
    missing_items = []
    
    # Check directories
    for directory in required_dirs:
        if os.path.exists(directory) and os.path.isdir(directory):
            print(f"✓ Directory: {directory}")
        else:
            print(f"✗ Directory missing: {directory}")
            missing_items.append(directory)
    
    # Check files
    for file_path in required_files:
        if os.path.exists(file_path) and os.path.isfile(file_path):
            print(f"✓ File: {file_path}")
        else:
            print(f"✗ File missing: {file_path}")
            missing_items.append(file_path)
    
    return len(missing_items) == 0, missing_items


def test_imports():
    """Test importing project modules"""
    print("\nTesting project imports...")
    
    # Add src to path
    sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))
    
    modules_to_test = [
        'models.moco',
        'data.dataset', 
        'attacks.mia',
        'utils.config',
        'utils.evaluation',
        'utils.metrics',
        'utils.visualization'
    ]
    
    failed_imports = []
    
    for module in modules_to_test:
        try:
            importlib.import_module(module)
            print(f"✓ {module} - OK")
        except ImportError as e:
            print(f"✗ {module} - FAILED: {str(e)}")
            failed_imports.append(module)
    
    return len(failed_imports) == 0, failed_imports


def test_config_loading():
    """Test configuration loading"""
    print("\nTesting configuration loading...")
    
    try:
        sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))
        from utils.config import load_config
        
        config = load_config()
        print("✓ Configuration loaded successfully")
        print(f"  Model architecture: {config.model.moco.arch}")
        print(f"  Training epochs: {config.training.num_epochs}")
        print(f"  Device: {config.device}")
        return True
    except Exception as e:
        print(f"✗ Configuration loading failed: {str(e)}")
        return False


def main():
    """Main test function"""
    print("=" * 60)
    print("CIFAR-100 MIA Framework - Installation Test")
    print("=" * 60)
    
    all_tests_passed = True
    
    # Run tests
    tests = [
        ("Python Version", test_python_version),
        ("Dependencies", test_dependencies),
        ("PyTorch CUDA", test_torch_cuda),
        ("Project Structure", test_project_structure),
        ("Project Imports", test_imports),
        ("Configuration Loading", test_config_loading)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            if test_name in ["Dependencies", "Project Structure", "Project Imports"]:
                result, details = test_func()
                results[test_name] = (result, details)
            else:
                result = test_func()
                results[test_name] = (result, None)
            
            if not result:
                all_tests_passed = False
        except Exception as e:
            print(f"✗ {test_name} test crashed: {str(e)}")
            results[test_name] = (False, str(e))
            all_tests_passed = False
    
    # Print summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    for test_name, (passed, details) in results.items():
        status = "PASSED" if passed else "FAILED"
        print(f"{test_name}: {status}")
        
        if not passed and details:
            if isinstance(details, list):
                print(f"  Missing: {', '.join(details)}")
            else:
                print(f"  Error: {details}")
    
    print("=" * 60)
    
    if all_tests_passed:
        print("🎉 All tests PASSED! The framework is ready to use.")
        print("\nNext steps:")
        print("1. Run: python example_usage.py")
        print("2. Or start with: python main.py --help")
    else:
        print("❌ Some tests FAILED. Please fix the issues above.")
        print("\nCommon solutions:")
        print("1. Install missing dependencies: pip install -r requirements.txt")
        print("2. Check Python version (requires 3.7+)")
        print("3. Verify project structure is complete")
    
    print("=" * 60)
    
    return all_tests_passed


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
